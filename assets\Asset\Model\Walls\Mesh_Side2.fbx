; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 20
		Hour: 17
		Minute: 28
		Second: 23
		Millisecond: 30
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side2.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side2.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:28:23.029"
			P: "Original|FileName", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side2.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:28:23.029"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle"
			P: "Original|ApplicationNativeFile", "KString", "", "", "C:\Users\<USER>\Desktop\Outsouces\object.ma"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2065351983504, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "object"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2065351761264, "Geometry::", "Mesh" {
		Vertices: *138 {
			a: -200,84.8572158813477,-50.3455467224121,-200,-97.7000045776367,-50.3455467224121,200,-97.7000045776367,-50.3456993103027,200,84.8572158813477,-50.3456993103027,-200,-97.7000045776367,50.3456993103027,200.000015258789,-97.7000045776367,50.3455467224121,200,97.6999969482422,-37.5029182434082,200.000015258789,97.6999969482422,50.3455467224121,-200,97.6999969482422,50.3456993103027,-200,97.6999969482422,-37.502758026123,-200,91.2786102294922,-48.6249313354492,-200,95.9793930053711,-43.9241523742676,-200,96.4861297607422,49.8475151062012,-200,231.688049316406,49.8475151062012,199.999984741211,96.4861297607422,49.8475151062012,-200,238.414978027344,48.9622192382813,-200,244.690155029297,46.3629608154297,-200,250.072814941406,42.2322883605957,199.999984741211,250.072814941406,42.2322883605957,199.999984741211,244.690155029297,46.3629570007324,199.999984741211,238.414978027344,48.9622192382813,199.999984741211,231.688049316406,49.8475151062012,199.999984741211,96.4861297607422,-49.8475151062012,199.999984741211,231.688049316406,-49.8475151062012,-200,96.4861297607422,-49.8475151062012,-200,231.688049316406,-49.8475151062012,-200,238.414978027344,-48.9622192382813,-200,244.690155029297,-46.3629570007324,-200,250.072814941406,-42.2322883605957,199.999984741211,238.414978027344,-48.9622192382813,199.999984741211,244.690155029297,-46.3629608154297,199.999984741211,250.072814941406,-42.2322883605957,200,91.2786102294922,-48.6250915527344,200,95.9793930053711,-43.9243087768555,199.999984741211,254.203491210938,-36.8496284484863,-200,254.203491210938,-36.8496284484863,-200,256.802734375,-30.5744533538818,199.999984741211,256.802734375,-30.5744533538818,-200,257.688049316406,-23.8475131988525,199.999984741211,257.688049316406,-23.8475151062012,-200,257.688049316406,23.8475151062012,-200,256.802734375,30.5744552612305,-200,254.203491210938,36.8496284484863,199.999984741211,257.688049316406,23.8475131988525,199.999984741211,256.802734375,30.5744552612305,199.999984741211,254.203491210938,36.8496284484863
		} 
		PolygonVertexIndex: *146 {
			a: 2,1,0,-4,4,1,2,-6,8,7,6,-10,1,4,8,-1,10,0,8,-12,11,8,-10,4,5,7,-9,13,12,14,-22,18,17,16,-20,19,16,15,-21,20,15,13,-22,23,22,24,-26,23,25,26,-30,29,26,27,-31,30,27,28,-32,10,32,3,-1,11,33,32,-11,9,6,33,-12,5,2,3,-8,7,3,32,-34,7,33,-7,35,34,31,-29,34,35,36,-38,37,36,38,-40,39,38,40,-44,43,40,41,-45,44,41,42,-46,45,42,17,-19,21,14,22,-24,12,13,25,-25,18,19,20,21,23,29,30,31,34,37,39,43,44,-46,15,16,17,42,41,40,38,36,35,28,27,26,25,-14
		} 
		Edges: *75 {
			a: 0,1,2,3,4,6,7,8,9,10,11,13,14,16,18,19,22,24,27,28,29,30,31,32,33,34,36,37,38,40,42,43,44,45,46,48,49,50,52,53,54,56,57,58,59,60,63,64,68,73,78,82,83,85,87,88,89,91,92,93,95,96,97,99,100,101,103,104,105,107,109,111,113,115,117
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *438 {
				a: -3.74155490590056e-07,0,-1,-3.74155490590056e-07,0,-1,-3.74155490590056e-07,0,-1,-3.74155490590056e-07,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,-1.60972835061557e-08,0,1,-1.60972835061557e-08,0,1,-1.60972835061557e-08,0,1,-1.60972835061557e-08,-1,0,4.54621073231465e-07,-1,0,4.54621073231465e-07,-1,2.80409184760799e-09,4.54678598771352e-07,-1,3.06747978129351e-08,4.55113678299313e-07,-1,1.61295736234024e-06,4.80524761314882e-07,-1,3.06747978129351e-08,4.55113678299313e-07,-1,2.80409184760799e-09,4.54678598771352e-07,-1,1.42663895985606e-06,4.81109736938379e-07,-1,1.42663895985606e-06,4.81109736938379e-07,-1,2.80409184760799e-09,4.54678598771352e-07,-1,-1.13063670141855e-05,5.21082597515488e-07,3.87017195180306e-07,0,1,3.87017195180306e-07,0,1,3.87017195180306e-07,0,1,3.87017195180306e-07,0,1,0,0.00623748265206814,0.999980568885803,0,0,1,0,0,1,0,0.00623748265206814,0.999980568885803,0,0.70710676908493,0.70710688829422,0,0.70710676908493,0.70710688829422,0,0.499959409236908,0.866048872470856,0,0.499959409236908,0.866048872470856,0,0.499959409236908,0.866048872470856,0,0.499959409236908,0.866048872470856,0,0.258864223957062,0.965913712978363,0,0.258864223957062,0.965913712978363,0,0.258864223957062,0.965913712978363,0,0.258864223957062,0.965913712978363,0,0.00623748265206814,0.999980568885803,0,0.00623748265206814,0.999980568885803,0,0.00623748265206814,-0.999980568885803,0,0,-1,0,0,-1,0,0.00623748265206814,-0.999980568885803,0,0.00623748265206814,-0.999980568885803,0,0.00623748265206814,-0.999980568885803,0,0.258864223957062,-0.965913712978363,0,0.258864223957062,-0.965913712978363,0,0.258864223957062,-0.965913712978363,0,0.258864223957062,-0.965913712978363,0,0.499959409236908,-0.866048872470856,0,0.499959409236908,-0.866048872470856,0,0.499959409236908,-0.866048872470856,0,0.499959409236908,-0.866048872470856,0,0.70710676908493,-0.70710688829422,0,0.70710676908493,-0.70710688829422,-3.89885059348671e-07,0.258819788694382,-0.965925633907318,-3.89885087770381e-07,0.258819788694382,-0.965925693511963,
-3.89885059348671e-07,0.258819788694382,-0.965925633907318,-3.89885087770381e-07,0.258819788694382,-0.965925693511963,-2.75213153599907e-07,0.707106590270996,-0.707107067108154,-2.75213182021616e-07,0.707106590270996,-0.70710700750351,-2.75213153599907e-07,0.707106590270996,-0.707107067108154,-2.75213182021616e-07,0.707106590270996,-0.70710700750351,-1.03204868651119e-07,0.965925812721252,-0.258819103240967,-1.03204875756546e-07,0.965925872325897,-0.258819103240967,-1.03204868651119e-07,0.965925812721252,-0.258819103240967,-1.03204875756546e-07,0.965925872325897,-0.258819103240967,1,0,-4.54621073231465e-07,1,0,-4.54621073231465e-07,1,7.0113898686941e-08,-4.55113735142731e-07,1,6.40981578925448e-09,-4.54678570349643e-07,1,6.40981578925448e-09,-4.54678570349643e-07,1,7.0113898686941e-08,-4.55113735142731e-07,1,3.68675978279498e-06,-4.80524704471463e-07,1,3.26088843394245e-06,-4.81109566408122e-07,1,6.40981578925448e-09,-4.54678570349643e-07,1,3.26088843394245e-06,-4.81109566408122e-07,1,-2.58431227848632e-05,-5.2108248382865e-07,0,0.866048991680145,-0.499959111213684,0,0.866048991680145,-0.499959111213684,0,0.70710676908493,-0.70710688829422,0,0.70710676908493,-0.70710688829422,0,0.866048991680145,-0.499959111213684,0,0.866048991680145,-0.499959111213684,0,0.965913414955139,-0.258865296840668,0,0.965913414955139,-0.258865296840668,0,0.965913414955139,-0.258865296840668,0,0.965913414955139,-0.258865296840668,0,0.999867796897888,-0.0162654370069504,0,0.999867796897888,-0.0162654370069504,0,0.999867796897888,-0.0162654370069504,0,0.999867796897888,-0.0162654370069504,8.80988129670079e-11,0.999867796897888,0.0162654351443052,8.80988129670079e-11,0.999867796897888,0.0162654351443052,8.80988129670079e-11,0.999867796897888,0.0162654351443052,8.80988129670079e-11,0.999867796897888,0.0162654351443052,3.56226298459106e-10,0.965913414955139,0.258865296840668,3.56226298459106e-10,0.965913414955139,0.258865296840668,3.56226298459106e-10,0.965913414955139,0.258865296840668,3.56226298459106e-10,0.965913414955139,0.258865296840668,
0,0.86604905128479,0.499959170818329,0,0.86604905128479,0.499959170818329,0,0.86604905128479,0.499959170818329,0,0.86604905128479,0.499959170818329,0,0.70710676908493,0.70710688829422,0,0.70710676908493,0.70710688829422,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,1.59928575271806e-07,0,1,1.59928603693515e-07,0,1,1.59928575271806e-07,0,1,1.59928589482661e-07,0,1,1.59928589482661e-07,0,1,1.59928575271806e-07,0,1,1.59928575271806e-07,0,1,1.59928575271806e-07,0,1,1.59928575271806e-07,0,1,1.59928575271806e-07,0,1,1.59928575271806e-07,0,1,1.59928589482661e-07,0,1,1.59928575271806e-07,0,1,1.59928575271806e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104168931022e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104168931022e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104225774441e-07,0,-1,-5.1310428261786e-07,0,-1,-5.13104225774441e-07,0,-1,-5.13104225774441e-07,0
			} 
			NormalsW: *146 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *100 {
				a: 0.388363003730774,0.196676462888718,0.38836258649826,0.196676462888718,0.38836258649826,0.339486598968506,0.388363003730774,0.339486598968506,0.110351577401161,0.196676462888718,0.110351964831352,0.196676462888718,0.110351577401161,0.349533170461655,0.110351964831352,0.349533170461655,0.352903723716736,0.349533170461655,0.352903306484222,0.349533170461655,0.383611917495728,0.344509899616241,0.370632976293564,0.348187237977982,0.636497557163239,0.365550071001053,0.111727103590965,0.34858363866806,0.111727058887482,0.34858363866806,0.111727058887482,0.454348891973495,0.656417667865753,0.379175812005997,0.656417727470398,0.379175812005997,0.64561253786087,0.37518647313118,0.64561253786087,0.37518647313118,0.638813316822052,0.37053570151329,0.638813257217407,0.37053570151329,0.897283315658569,0.365550071001053,0.386987507343292,0.34858363866806,0.386987507343292,0.34858363866806,0.386987507343292,0.454348891973495,0.894967555999756,0.37053570151329,0.894967555999756,0.37053570151329,0.888168394565582,0.37518647313118,0.888168275356293,0.37518647313118,0.87736314535141,0.379175812005997,0.87736314535141,0.379175812005997,0.383612334728241,0.344509899616241,0.370633333921432,0.348187237977982,0.863283038139343,0.382237166166306,0.863283038139343,0.382237166166306,0.846868216991425,0.384163588285446,0.846868097782135,0.384163588285446,0.829271614551544,0.384819716215134,0.829271614551544,0.384819716215134,0.704509198665619,0.384819716215134,0.704509198665619,0.384819716215134,0.686912715435028,0.384163588285446,0.686912596225739,0.384163588285446,0.670497834682465,0.382237166166306,0.67049777507782,0.382237166166306,0.111727103590965,0.454348891973495,0.897283315658569,0.365550071001053,0.386987507343292,0.454348891973495,0.636497497558594,0.365550071001053
			} 
			UVIndex: *146 {
				a: 0,1,2,3,4,1,0,5,6,7,8,9,1,4,6,2,10,2,6,11,11,6,9,4,5,7,6,46,13,14,15,16,17,18,19,19,18,20,21,21,20,12,49,48,23,24,25,22,47,26,27,27,26,28,29,29,28,30,31,10,32,3,2,11,33,32,10,9,8,33,11,5,0,3,7,7,3,32,33,7,33,8,34,35,31,30,35,34,36,37,37,36,38,39,39,38,40,41,41,40,42,43,43,42,44,45,45,44,17,16,15,14,23,48,13,46,25,24,16,19,21,49,22,27,29,31,35,37,39,41,43,45,20,18,17,44,42,40,38,36,34,30,28,26,47,12
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2065197368576, "Model::Side2", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A",0,-79.994026184082,0
			P: "currentUVSet", "KString", "", "U", "UVSet0"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2064700110976, "Material::Default_Material3", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	AnimationStack: 2065358517088, "AnimStack::object", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",1924423250
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",1924423250
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 2065074435168, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Side2, Model::RootNode
	C: "OO",2065197368576,0
	
	;AnimLayer::BaseLayer, AnimStack::object
	C: "OO",2065074435168,2065358517088
	
	;Geometry::, Model::Side2
	C: "OO",2065351761264,2065197368576
	
	;Material::Default_Material3, Model::Side2
	C: "OO",2064700110976,2065197368576
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "object"
	Take: "object" {
		FileName: "object.tak"
		LocalTime: 1924423250,230930790000
		ReferenceTime: 1924423250,230930790000
	}
}
