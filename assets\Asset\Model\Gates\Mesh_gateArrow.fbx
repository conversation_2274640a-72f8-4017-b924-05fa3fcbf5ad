; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 10
		Hour: 17
		Minute: 26
		Second: 28
		Millisecond: 70
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Downloads\OUTSOURCE\OUTSOURCE\FBX\Gate\Mesh_gateArrow.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Downloads\OUTSOURCE\OUTSOURCE\FBX\Gate\Mesh_gateArrow.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "10/03/2025 10:26:28.069"
			P: "Original|FileName", "KString", "", "", "C:\Users\<USER>\Downloads\OUTSOURCE\OUTSOURCE\FBX\Gate\Mesh_gateArrow.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "10/03/2025 10:26:28.069"
			P: "Original|ApplicationActiveProject", "KString", "", "", "C:\Users\<USER>\Downloads\OUTSOURCE\OUTSOURCE\FBX\Gate"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2133770960752, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "Take 001"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2133531406480, "Geometry::", "Mesh" {
		Vertices: *315 {
			a: -12.4679641723633,253.032272338867,21.90016746521,-16.4370441436768,253.032272338867,20.8815879821777,-19.4251403808594,253.032272338867,18.0775966644287,-20.6937313079834,253.032272338867,14.1812171936035,-19.9292736053467,253.032272338867,10.1554622650146,19.9292736053467,253.032272338867,10.1554622650146,20.6937313079834,253.032272338867,14.1812171936035,19.4251403808594,253.032272338867,18.0775966644287,16.4370441436768,253.032272338867,20.8815879821777,12.4679641723633,253.032272338867,21.90016746521,-6.95717668533325,253.032272338867,-17.4803333282471,-4.12120246887207,253.032272338867,-20.7017765045166,0,253.032272338867,-21.90016746521,4.12120246887207,253.032272338867,-20.7017765045166,6.95717668533325,253.032272338867,-17.4803333282471,-13.9856767654419,269.755767822266,12.0807542800903,-16.2601928710938,269.283935546875,11.3439769744873,-18.1884346008301,267.940277099609,10.7193670272827,-19.4768447875977,265.929382324219,10.3020162582397,-19.9292736053467,263.557342529297,10.1554622650146,-6.95717668533325,263.557342529297,-17.4803333282471,-6.5586199760437,265.929382324219,-17.2190093994141,-5.42362689971924,267.940277099609,-16.4748191833496,-3.72498965263367,269.283935546875,-15.3610591888428,-1.72131037712097,269.755767822266,-14.047290802002,-12.4679641723633,263.557342529297,21.90016746521,-12.4083871841431,265.929382324219,21.4283409118652,-12.2387266159058,267.940277099609,20.0846900939941,-11.9848117828369,269.283935546875,18.0737743377686,-11.6852979660034,269.755767822266,15.7017383575439,11.6852979660034,269.755767822266,15.7017383575439,11.9848117828369,269.283935546875,18.0737743377686,12.2387266159058,267.940277099609,20.0846900939941,12.4083871841431,265.929382324219,21.4283409118652,12.4679641723633,263.557342529297,21.90016746521,19.9292736053467,263.557342529297,10.1554622650146,19.4768447875977,265.929382324219,10.3020162582397,18.1884346008301,267.940277099609,10.7193670272827,16.2601928710938,269.283935546875,11.3439769744873,13.9856767654419,269.755767822266,12.0807542800903,
1.72131037712097,269.755767822266,-14.047290802002,3.72498965263367,269.283935546875,-15.3610591888428,5.42362689971924,267.940277099609,-16.4748191833496,6.5586199760437,265.929382324219,-17.2190093994141,6.95717668533325,263.557342529297,-17.4803333282471,-14.3073654174805,269.755767822266,13.7748136520386,-16.7513217926025,269.283935546875,13.9303379058838,-18.8232078552246,267.940277099609,14.0621843338013,-20.2075977325439,265.929382324219,14.1502819061279,-20.6937313079834,263.557342529297,14.1812171936035,-14.0236892700195,269.755767822266,14.6461019515991,-16.0907344818115,269.283935546875,15.9592781066895,-17.8430919647217,267.940277099609,17.0725364685059,-19.0139789581299,265.929382324219,17.8163909912109,-19.4251403808594,263.557342529297,18.0775966644287,-13.355507850647,269.755767822266,15.2731142044067,-14.5347604751587,269.283935546875,17.4193840026855,-15.5344829559326,267.940277099609,19.2389049530029,-16.2024765014648,265.929382324219,20.4546680450439,-16.4370441436768,263.557342529297,20.8815879821777,13.355507850647,269.755767822266,15.2731142044067,14.5347604751587,269.283935546875,17.4193840026855,15.5344829559326,267.940277099609,19.2389049530029,16.2024765014648,265.929382324219,20.4546680450439,16.4370441436768,263.557342529297,20.8815879821777,14.0236892700195,269.755767822266,14.6461019515991,16.0907344818115,269.283935546875,15.9592781066895,17.8430919647217,267.940277099609,17.0725364685059,19.0139789581299,265.929382324219,17.8163909912109,19.4251403808594,263.557342529297,18.0775966644287,14.3073654174805,269.755767822266,13.7748136520386,16.7513217926025,269.283935546875,13.9303379058838,18.8232078552246,267.940277099609,14.0621843338013,20.2075977325439,265.929382324219,14.1502819061279,20.6937313079834,263.557342529297,14.1812171936035,-4.12120246887207,263.557342529297,-20.7017765045166,-3.85771417617798,265.929382324219,-20.2870254516602,-3.10736322402954,267.940277099609,-19.1059112548828,-1.98438346385956,269.283935546875,-17.3382511138916,-0.659738600254059,269.755767822266,-15.25315284729,
2.93586860534702e-16,263.557342529297,-21.90016746521,3.56074942743154e-16,265.929382324219,-21.4087963104248,2.30033528515958e-16,267.940277099609,-20.0094909667969,4.84830329942692e-16,269.283935546875,-17.915283203125,7.85383612952087e-16,269.755767822266,-15.444995880127,4.12120246887207,263.557342529297,-20.7017765045166,3.85771417617798,265.929382324219,-20.2870254516602,3.10736322402954,267.940277099609,-19.1059112548828,1.98438346385956,269.283935546875,-17.3382511138916,0.659738600254059,269.755767822266,-15.25315284729,7.77166228693417e-16,269.755767822266,-14.6118602752686,0.212984919548035,269.755767822266,-14.5499267578125,-0.212984919548035,269.755767822266,-14.5499267578125,-1.04554343223572,269.755767822266,-13.6042051315308,-13.2185668945313,269.755767822266,12.3292427062988,-13.4831094741821,269.755767822266,13.7223615646362,-13.3265514373779,269.755767822266,14.2032155990601,-12.9577894210815,269.755767822266,14.5492572784424,-11.584282875061,269.755767822266,14.9017381668091,11.584282875061,269.755767822266,14.9017381668091,12.9577894210815,269.755767822266,14.5492572784424,13.3265514373779,269.755767822266,14.2032155990601,13.4831094741821,269.755767822266,13.7223615646362,13.2185668945313,269.755767822266,12.3292427062988,1.04554343223572,269.755767822266,-13.6042051315308
		} 
		PolygonVertexIndex: *399 {
			a: 19,18,21,-21,18,17,22,-22,17,16,23,-23,16,15,24,-24,29,28,31,-31,28,27,32,-32,27,26,33,-33,26,25,34,-34,39,38,41,-41,38,37,42,-42,37,36,43,-43,36,35,44,-44,25,0,9,-35,35,5,14,-45,20,10,4,-20,4,3,49,-20,3,2,54,-50,2,1,59,-55,1,0,25,-60,9,8,64,-35,8,7,69,-65,7,6,74,-70,6,5,35,-75,20,75,11,-11,75,80,12,-12,80,85,13,-13,85,44,14,-14,91,90,-105,90,92,-105,92,93,-105,93,94,-105,95,96,-95,96,97,-95,97,98,-95,94,98,-105,100,101,-100,101,102,-100,102,103,-100,99,103,-99,98,103,-105,18,19,49,-49,17,18,48,-48,15,16,46,-46,16,17,47,-47,23,24,79,-79,22,23,78,-78,20,21,76,-76,21,22,77,-77,33,34,64,-64,32,33,63,-63,30,31,61,-61,31,32,62,-62,48,49,54,-54,47,48,53,-53,45,46,51,-51,46,47,52,-52,53,54,59,-59,52,53,58,-58,50,51,56,-56,51,52,57,-57,25,26,58,-60,26,27,57,-59,27,28,56,-58,28,29,55,-57,63,64,69,-69,62,63,68,-68,60,61,66,-66,61,62,67,-67,68,69,74,-74,67,68,73,-73,65,66,71,-71,66,67,72,-72,35,36,73,-75,36,37,72,-74,37,38,71,-73,38,39,70,-72,78,79,84,-84,77,78,83,-83,75,76,81,-81,76,77,82,-82,83,84,89,-89,82,83,88,-88,80,81,86,-86,81,82,87,-87,40,41,88,-90,41,42,87,-89,42,43,86,-88,43,44,85,-87,89,84,90,-92,84,79,92,-91,79,24,93,-93,24,15,94,-94,15,45,95,-95,45,50,96,-96,50,55,97,-97,55,29,98,-98,29,30,99,-99,30,60,100,-100,60,65,101,-101,65,70,102,-102,70,39,103,-103,39,40,104,-104,40,89,91,-105
		} 
		Edges: *207 {
			a: 49,57,53,60,64,68,72,76,80,84,88,94,98,102,106,157,158,161,153,62,149,0,1,2,3,4,5,6,8,9,10,12,13,14,173,92,177,169,164,165,74,240,16,17,18,19,20,21,22,24,25,26,28,29,30,189,190,193,185,78,181,90,288,32,33,34,35,36,37,38,40,41,42,44,45,46,326,104,205,206,209,201,66,197,221,222,225,217,70,213,253,254,257,249,82,245,269,270,273,265,86,261,301,96,305,297,292,293,317,100,321,313,308,309,48,50,52,54,56,58,61,65,69,77,81,85,93,97,101,150,154,156,166,170,172,182,186,188,198,202,204,214,218,220,228,232,236,246,250,252,262,266,268,276,280,284,294,298,300,310,314,316,324,328,332,340,342,108,344,111,348,114,352,117,356,122,360,120,364,123,368,126,372,143,376,134,380,132,384,135,388,138,392,145,110,109,112,115,118,121,124,127,130,133,136,139,142
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByVertice"
			ReferenceInformationType: "Direct"
			Normals: *315 {
				a: -0.0352169908583164,0,0.999379694461823,-0.481543362140656,0,0.87642228603363,-0.84407103061676,0,0.536231338977814,-0.997981369495392,0,0.0635076761245728,-0.917380571365356,0,-0.398011118173599,0.917380571365356,0,-0.398011118173599,0.997981369495392,0,0.0635076761245728,0.84407103061676,0,0.536231338977814,0.481543362140656,0,0.87642228603363,0.0352169908583164,0,0.999379694461823,-0.890006601810455,0,-0.455947726964951,-0.536230981349945,0,-0.844071328639984,0,0,-1,0.536230981349945,0,-0.844071328639984,0.890006601810455,0,-0.455947726964951,-0.1400246322155,0.988154888153076,-0.0627926737070084,-0.365000396966934,0.916844844818115,-0.161772340536118,-0.655893504619598,0.697867810726166,-0.287722527980804,-0.849356055259705,0.376482218503952,-0.369939714670181,-0.916745781898499,0.0360777638852596,-0.397838562726974,-0.889483034610748,0.0360451340675354,-0.455544382333755,-0.82547914981842,0.375948458909988,-0.421006947755814,-0.639887273311615,0.696975886821747,-0.32368016242981,-0.35870423913002,0.916075766086578,-0.179266646504402,-0.138256505131721,0.988050699234009,-0.0681249648332596,-0.0350857675075531,0.0360755622386932,0.99873298406601,-0.0311105046421289,0.376388221979141,0.925939619541168,-0.0218955911695957,0.697651028633118,0.716103076934814,-0.0104509787634015,0.916598916053772,0.399671524763107,-0.0032257242128253,0.988132357597351,0.153570756316185,0.00322571885772049,0.988132357597351,0.153570905327797,0.0104509769007564,0.916598856449127,0.399671733379364,0.0218955930322409,0.697651028633118,0.716103076934814,0.0311105046421289,0.376388281583786,0.925939619541168,0.0350857675075531,0.0360755659639835,0.99873298406601,0.916745781898499,0.0360777676105499,-0.397838562726974,0.849356055259705,0.376482158899307,-0.369939774274826,0.655893504619598,0.697867810726166,-0.287722557783127,0.365000396966934,0.916844666004181,-0.16177336871624,0.140024542808533,0.988154828548431,-0.0627945065498352,0.138256385922432,0.988050520420074,-0.0681273937225342,0.358704060316086,0.916075348854065,-0.179268419742584,
0.639887273311615,0.696975767612457,-0.323680490255356,0.82547914981842,0.375948458909988,-0.421006917953491,0.889483034610748,0.0360451303422451,-0.455544382333755,-0.165247768163681,0.986247420310974,0.00302453362382948,-0.433591395616531,0.900918960571289,0.0185366272926331,-0.726588308811188,0.685913264751434,0.039908017963171,-0.926772177219391,0.371360808610916,0.0564317479729652,-0.99734628200531,0.0359763987362385,0.06329345703125,-0.143828824162483,0.985375702381134,0.0913669019937515,-0.374292999505997,0.896305620670319,0.237783461809158,-0.616702020168304,0.682776689529419,0.391784012317657,-0.784249186515808,0.369765102863312,0.498223781585693,-0.84352844953537,0.0358620770275593,0.53588604927063,-0.0729541033506393,0.986248254776001,0.148297175765038,-0.201019734144211,0.900919616222382,0.384623020887375,-0.344863176345825,0.68591445684433,0.640773475170135,-0.444882184267044,0.371361434459686,0.81496649980545,-0.481079936027527,0.0359761863946915,0.875938236713409,0.0729541331529617,0.986248552799225,0.148295119404793,0.201020106673241,0.900920033454895,0.384621769189835,0.344863295555115,0.685915052890778,0.640772938728333,0.444882273674011,0.371361345052719,0.81496661901474,0.481079906225204,0.0359762087464333,0.875938236713409,0.143828809261322,0.985376000404358,0.0913638547062874,0.374293386936188,0.896306574344635,0.237779200077057,0.616702258586884,0.682777464389801,0.391782283782959,0.784249246120453,0.369764924049377,0.498223900794983,0.843528389930725,0.0358620956540108,0.535885989665985,0.165247708559036,0.986247479915619,0.00303043494932353,0.433591306209564,0.900918960571289,0.0185381099581718,0.726588129997253,0.685913383960724,0.0399069599807262,0.926772236824036,0.371360659599304,0.0564313940703869,0.99734628200531,0.0359764061868191,0.0632935911417007,-0.536061465740204,0.0360953323543072,-0.843406975269318,-0.500455439090729,0.371655493974686,-0.781931340694427,-0.397159487009048,0.685244202613831,-0.610495507717133,-0.245150819420815,0.898531556129456,-0.364063054323196,-0.0983323305845261,0.985884249210358,-0.135510757565498,
0,0.0359705500304699,-0.999352872371674,0,0.369892120361328,-0.929074645042419,-6.79591565244664e-08,0.681700646877289,-0.73163115978241,-1.49725934761591e-07,0.892872333526611,-0.450309813022614,0,0.984654307365417,-0.174516603350639,0.536061465740204,0.0360953249037266,-0.843406975269318,0.500455498695374,0.371655493974686,-0.781931281089783,0.397159159183502,0.685243248939514,-0.610496819019318,0.245150327682495,0.898530721664429,-0.364065647125244,0.0983323305845261,0.985884070396423,-0.135511294007301,0,1,3.80789933842607e-05,0,1,-9.45243118621875e-06,0,1,-7.92760420154082e-06,0,1,-4.66703295387561e-06,0,0.999999940395355,4.74557708685097e-07,0,1,0,0,0.999999940395355,4.37826565757859e-06,0,1,9.1056263045175e-06,0,1,3.0385646709874e-07,0,1,-1.58256079885177e-06,0,1,4.5941869757371e-06,0,1,4.69207725473098e-06,0,1,8.62261367728934e-06,0,0.999999940395355,8.87287114892388e-07,0,0.999999940395355,2.94651307797267e-08
			} 
			NormalsW: *105 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *210 {
				a: 0.268141984939575,0.629358291625977,0.264796078205109,0.771197319030762,0.893125176429749,0.771197319030762,0.899091482162476,0.629358291625977,0.255267560482025,0.891441345214844,0.876134634017944,0.891441345214844,0.241007208824158,0.971786499023438,0.850706577301025,0.971786499023438,0.224185883998871,1,0.820712089538574,1,0.141515612602234,1,0.0873600244522095,0.971786499023438,0.0873600244522095,0.971786499023438,0.141515612602234,1,0.0414490103721619,0.891441345214844,0.0414490103721619,0.891441345214844,0.0107722878456116,0.771197319030762,0.0107722878456116,0.771197319030762,5.96046447753906e-08,0.629358291625977,5.96046447753906e-08,0.629358291625977,0.224185883998871,1,0.241007149219513,0.971786499023438,0.850706577301025,0.971786499023438,0.820712089538574,1,0.255267560482025,0.891441345214844,0.876134634017944,0.891441345214844,0.264796018600464,0.771197319030762,0.893125176429749,0.771197319030762,0.268141984939575,0.629358291625977,0.899091482162476,0.629358291625977,5.96046447753906e-08,0,5.96046447753906e-08,0,0.268141984939575,0,0.899091482162476,0,0.899091482162476,0,0.268141984939575,0,0.176230549812317,0,0.176230549812317,0.629358291625977,0.0872727632522583,0,0.0872727632522583,0.629358291625977,0.0232551693916321,0,0.0232551693916321,0.629358291625977,0.0232551097869873,0,0.0232551097869873,0.629358291625977,0.0872727036476135,0,0.0872727036476135,0.629358291625977,0.176230430603027,0,0.176230430603027,0.629358291625977,0.972639799118042,0.629358291625977,0.972639799118042,0,1.00000023841858,0.629358291625977,1.00000023841858,0,0.972639799118042,0.629358291625977,0.972639799118042,0,0.832187652587891,1,0.833601713180542,1,0.810595989227295,1,0.832187652587891,1,0.810595989227295,1,0.218512654304504,1,0.18670654296875,1,0.17572820186615,1,0.167827844619751,1,0.159780383110046,1,0.167827844619751,1,0.17572820186615,1,0.159780383110046,1,0.18670654296875,1,0.218512654304504,1,0.17693680524826,0.771197319030762,0.178948163986206,0.891441345214844,0.181958317756653,0.971786499023438,
0.185509026050568,1,0.848242998123169,1,0.89584755897522,0.971786499023438,0.93620491027832,0.891441345214844,0.963170766830444,0.771197319030762,0.0330020785331726,0.771197319030762,0.0607590079307556,0.891441345214844,0.10230028629303,0.971786499023438,0.151301503181458,1,0.0932363271713257,0.771197319030762,0.110219120979309,0.891441345214844,0.135635852813721,0.971786499023438,0.165616750717163,1,0.0330021381378174,0.771197319030762,0.0607590675354004,0.891441345214844,0.10230028629303,0.971786499023438,0.151301503181458,1,0.0932362079620361,0.771197319030762,0.110219120979309,0.891441345214844,0.135635733604431,0.971786499023438,0.165616750717163,1,0.176936745643616,0.771197319030762,0.178948044776917,0.891441345214844,0.181958258152008,0.971786499023438,0.185509026050568,1,0.852622985839844,1,0.909021735191345,0.971786499023438,0.956834316253662,0.891441345214844,0.988781690597534,0.771197319030762,0.848242998123169,1,0.89584755897522,0.971786499023438,0.93620491027832,0.891441345214844,0.963170766830444,0.771197319030762
			} 
			UVIndex: *399 {
				a: 0,1,2,3,1,4,5,2,4,6,7,5,6,8,9,7,10,11,12,13,11,14,15,12,14,16,17,15,16,18,19,17,20,21,22,23,21,24,25,22,24,26,27,25,26,28,29,27,18,30,31,19,28,32,33,29,3,34,35,0,35,36,37,0,36,38,39,37,38,40,41,39,40,30,18,41,31,42,43,19,42,44,45,43,44,46,47,45,46,32,28,47,3,48,49,34,48,50,51,49,50,52,53,51,52,29,33,53,54,55,56,55,57,56,57,58,56,58,59,56,60,61,59,61,62,59,62,63,59,59,63,56,64,65,66,65,67,66,67,68,66,66,68,63,63,68,56,1,0,37,69,4,1,69,70,8,6,71,72,6,4,70,71,7,9,73,74,5,7,74,75,3,2,76,48,2,5,75,76,17,19,43,77,15,17,77,78,13,12,79,80,12,15,78,79,69,37,39,81,70,69,81,82,72,71,83,84,71,70,82,83,81,39,41,85,82,81,85,86,84,83,87,88,83,82,86,87,18,16,85,41,16,14,86,85,14,11,87,86,11,10,88,87,77,43,45,89,78,77,89,90,80,79,91,92,79,78,90,91,89,45,47,93,90,89,93,94,92,91,95,96,91,90,94,95,28,26,93,47,26,24,94,93,24,21,95,94,21,20,96,95,74,73,97,98,75,74,98,99,48,76,100,50,76,75,99,100,98,97,101,102,99,98,102,103,50,100,104,52,100,99,103,104,23,22,102,101,22,25,103,102,25,27,104,103,27,29,52,104,101,97,55,54,97,73,57,55,73,9,58,57,9,8,59,58,8,72,60,59,72,84,61,60,84,88,62,61,88,10,63,62,10,13,66,63,13,80,64,66,80,92,65,64,92,96,67,65,96,20,68,67,20,23,56,68,23,101,54,56
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2132442202880, "Model::Mesh_gateArrow", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2132640375520, "Material::Mat_gateArrow", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0.5,0.5,0.5
			P: "DiffuseFactor", "Number", "", "A",0.800000011920929
			P: "TransparencyFactor", "Number", "", "A",1
			P: "SpecularColor", "Color", "", "A",0.5,0.5,0.5
			P: "ShininessExponent", "Number", "", "A",6.31179093816824
			P: "ReflectionFactor", "Number", "", "A",0.5
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0.400000005960464,0.400000005960464,0.400000005960464
			P: "Specular", "Vector3D", "Vector", "",0.5,0.5,0.5
			P: "Shininess", "double", "Number", "",6.31179093816824
			P: "Opacity", "double", "Number", "",1
			P: "Reflectivity", "double", "Number", "",0
		}
	}
	AnimationStack: 2133948782448, "AnimStack::Take 001", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",1924423250
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",1924423250
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 2133457755536, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh_gateArrow, Model::RootNode
	C: "OO",2132442202880,0
	
	;AnimLayer::BaseLayer, AnimStack::Take 001
	C: "OO",2133457755536,2133948782448
	
	;Geometry::, Model::Mesh_gateArrow
	C: "OO",2133531406480,2132442202880
	
	;Material::Mat_gateArrow, Model::Mesh_gateArrow
	C: "OO",2132640375520,2132442202880
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "Take 001"
	Take: "Take 001" {
		FileName: "Take_001.tak"
		LocalTime: 1924423250,230930790000
		ReferenceTime: 1924423250,230930790000
	}
}
