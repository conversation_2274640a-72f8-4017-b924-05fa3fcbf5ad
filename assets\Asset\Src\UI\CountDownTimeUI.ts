import { _decorator, Component, Label, Node } from 'cc';

import gameEndHandler from "db://assets/PLAGameFoundation/gameControl/utilities/handler/gameEndHandler";

import {GameManager} from "db://assets/PLAGameFoundation/gameControl/core/manager/gameManager";
import {Constant} from "db://assets/constant/constant";
import {GameControl} from "db://assets/Asset/Src/Core/GameControl";
import {ControlUI} from "db://assets/Asset/Src/Core/ControlUI";

const { ccclass, property } = _decorator;

@ccclass('CountDownTimeUI')
export class CountDownTimeUI extends Component {
    @property(Label)
    private timeLabel: Label = null;
    @property
    totalTime: number = 0;
    private isRunning: boolean = false;
    static _instance : CountDownTimeUI;
    static get instance(){
        return this._instance;
    }
    onLoad(){
        CountDownTimeUI._instance = this;
        this.updateTimeLabel();
    }
    start() {
        // this.startTimer();
    }

    private updateTimeLabel() {
        let minutes = Math.floor(this.totalTime / 60);
        let seconds = this.totalTime % 60;
        this.timeLabel.string = `${this.formatTime(minutes)}:${this.formatTime(seconds)}`;
    }

    private formatTime(value: number): string {
        return value < 10 ? `0${value}` : `${value}`;
    }

    startTimer() {
        if (this.isRunning) return;
        this.isRunning = true;
        this.schedule(this.updateTimer, 1);
    }

    private updateTimer() {
        this.totalTime--;
        if(this.totalTime == 0 || GameControl.instance.checkWin){
            this.stopTimer();
        }
        // if (this.totalTime == 35){
        //     GameControl.instance.checkWin = true;
        //     this.stopTimer();
        //     ControlUI.instance.popUpWin.active = true;
        //     gameEndHandler.StoreGameChangingNoCondition();
        // }
        if(this.totalTime < 0) return;
        this.updateTimeLabel();
    }

    stopTimer() {
        this.isRunning = false;
        if (!GameControl.instance.checkWin){
            GameControl.instance.checkLose = true;
            GameControl._instance.popUpTryAgain.active = true;
            GameControl._instance.warningNode.active = false;
            ControlUI.instance.playNow.active = false;
        }
        // gameEndHandler.StoreGameChangingNoCondition();
        // console.log('stop')
        this.unschedule(this.updateTimer);
    }

    resetTimer() {
        this.stopTimer();
        this.totalTime = 0;
        this.updateTimeLabel();
    }
}