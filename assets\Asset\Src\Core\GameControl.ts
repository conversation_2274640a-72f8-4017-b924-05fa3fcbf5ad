import { _decorator, Component, Node, Prefab } from 'cc';
import {GameManager} from "db://assets/PLAGameFoundation/gameControl/core/manager/gameManager";
const { ccclass, property } = _decorator;

@ccclass('GameControl')
export class GameControl extends Component {
    static _instance : GameControl;
    static get instance(){
        return this._instance;
    }
    @property(Node)
    popUpTryAgain : Node = null;
    @property(Node)
    warningNode : Node = null;
    public checkWin : boolean = false;
    public checkLose : boolean = false;
    public checkFirst : boolean = false;
    public checkCanTouch : boolean = false;
    onLoad(){
        GameControl._instance = this;
        GameManager.instance.SetupManagerInit();
    }
}


