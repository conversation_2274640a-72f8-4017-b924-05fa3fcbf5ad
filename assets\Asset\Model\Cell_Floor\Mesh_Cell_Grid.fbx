; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 12
		Hour: 20
		Minute: 1
		Second: 9
		Millisecond: 730
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Cell\Mesh_grid.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Cell\Mesh_grid.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "12/03/2025 13:01:09.729"
			P: "Original|FileName", "KString", "", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Cell\Mesh_grid.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "12/03/2025 13:01:09.729"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Cell"
			P: "Original|ApplicationNativeFile", "KString", "", "", "C:\Users\<USER>\Desktop\Outsouces\Puzzle\Block\Block_2.ma"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1970104277504, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "Block_2"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1969100600496, "Geometry::", "Mesh" {
		Vertices: *60 {
			a: -98.4777450561523,-87.654899597168,-2.33120954362676e-05,-94.1427230834961,-94.1427230834961,-2.39484998019179e-05,-87.654899597168,-98.4777450561523,-2.43737322307425e-05,-80.0019989013672,-100,-2.45230530708795e-05,-100,-80.0019989013672,-2.25614057853818e-05,87.654899597168,-98.4777450561523,-2.43737322307425e-05,94.1427230834961,-94.1427230834961,-2.39484998019179e-05,98.4777450561523,-87.654899597168,-2.33120954362676e-05,100,-80.0019989013672,-2.25614057853818e-05,80.0019989013672,-100,-2.45230530708795e-05,-87.654899597168,98.4777450561523,-5.05393290950451e-06,-94.1427230834961,94.1427230834961,-5.47916442883434e-06,-98.4777450561523,87.654899597168,-6.11556924923207e-06,-100,80.0019989013672,-6.86625935486518e-06,-80.0019989013672,100,-4.90461115987273e-06,98.4777450561523,87.654899597168,-6.11556924923207e-06,94.1427230834961,94.1427230834961,-5.47916442883434e-06,87.654899597168,98.4777450561523,-5.05393290950451e-06,80.0019989013672,100,-4.90461115987273e-06,100,80.0019989013672,-6.86625935486518e-06
		} 
		PolygonVertexIndex: *20 {
			a: 15,16,17,18,14,10,11,12,13,4,0,1,2,3,9,5,6,7,8,-20
		} 
		Edges: *20 {
			a: 13,18,8,3,12,11,10,9,17,16,15,14,7,6,5,4,2,1,0,19
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByVertice"
			ReferenceInformationType: "Direct"
			Normals: *60 {
				a: -2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,1,-2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355,-2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,1,-2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355,-2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,1,-2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355,-2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,1,-2.38708559382073e-15,-9.80922152393759e-08,1,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355,-2.38708538206249e-15,-9.80922223448033e-08,0.999999940395355
			} 
			NormalsW: *20 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *40 {
				a: 0.18824777007103,0.578804612159729,0.190825045108795,0.770414054393768,0.187275871634483,0.359234392642975,0.20007535815239,0.491522818803787,0.188617140054703,0.354669034481049,0.188002467155457,0.53261524438858,0.189483746886253,0.774979948997498,0.200320646166801,0.537711679935455,-0,-0,0.188093289732933,0.554285168647766,-0,-0,-0,-0,0.189638212323189,0.799498915672302,-0,-0,0.187901094555855,0.346116751432419,0.187185049057007,0.337564438581467,-0,-0,-0,-0,0.201507449150085,0.508627414703369,0.200791403651237,0.500075101852417
			} 
			UVIndex: *20 {
				a: 17,18,19,3,4,14,15,16,2,5,8,9,10,0,6,11,12,13,1,7
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 1970059113984, "Model::Mesh_grid", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "currentUVSet", "KString", "", "U", "UVSet0"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 1969880690544, "Material::Mat_grid", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0.5,0.5,0.5
			P: "DiffuseFactor", "Number", "", "A",0.800000011920929
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0.400000005960464,0.400000005960464,0.400000005960464
			P: "Opacity", "double", "Number", "",1
		}
	}
	AnimationStack: 1968745350848, "AnimStack::Block_2", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",1924423250
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",1924423250
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 1968985950880, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh_grid, Model::RootNode
	C: "OO",1970059113984,0
	
	;AnimLayer::BaseLayer, AnimStack::Block_2
	C: "OO",1968985950880,1968745350848
	
	;Geometry::, Model::Mesh_grid
	C: "OO",1969100600496,1970059113984
	
	;Material::Mat_grid, Model::Mesh_grid
	C: "OO",1969880690544,1970059113984
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "Block_2"
	Take: "Block_2" {
		FileName: "Block_2.tak"
		LocalTime: 1924423250,230930790000
		ReferenceTime: 1924423250,230930790000
	}
}
