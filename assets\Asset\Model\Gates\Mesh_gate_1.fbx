; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 13
		Hour: 11
		Minute: 53
		Second: 0
		Millisecond: 295
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate\Mesh_gate_1.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate\Mesh_gate_1.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "13/03/2025 04:53:00.294"
			P: "Original|FileName", "KString", "", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate\Mesh_gate_1.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "13/03/2025 04:53:00.294"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Front"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2478592041440, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "Take 001"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2479089013616, "Geometry::", "Mesh" {
		Vertices: *96 {
			a: -100,96.3901596069336,49.8475151062012,-100,231.592056274414,49.8474998474121,100,96.3901596069336,49.8475151062012,-100,238.318969726563,48.9622039794922,-100,244.594146728516,46.3629455566406,-100,249.976821899414,42.2322769165039,100,249.976821899414,42.2322769165039,100,244.594146728516,46.3629455566406,100,238.318969726563,48.9622039794922,100,231.592056274414,49.8474998474121,100,96.39013671875,-49.8475151062012,100,231.592056274414,-49.847526550293,-100,96.39013671875,-49.8475151062012,-100,231.592056274414,-49.847526550293,-100,238.318969726563,-48.962230682373,-100,244.594146728516,-46.362964630127,-100,249.976821899414,-42.2323036193848,100,238.318969726563,-48.962230682373,100,244.594146728516,-46.362964630127,100,249.976821899414,-42.2323036193848,100,254.107498168945,-36.8496398925781,-100,254.107498168945,-36.8496398925781,-100,256.706756591797,-30.5744743347168,100,256.706756591797,-30.5744705200195,-100,257.592071533203,-23.8475341796875,100,257.592071533203,-23.8475341796875,-100,257.592071533203,23.8474941253662,-100,256.706756591797,30.5744342803955,-100,254.107498168945,36.849609375,100,257.592071533203,23.8474941253662,100,256.706756591797,30.5744342803955,100,254.107498168945,36.8496131896973
		} 
		PolygonVertexIndex: *96 {
			a: 1,0,2,-10,6,5,4,-8,7,4,3,-9,8,3,1,-10,11,10,12,-14,11,13,14,-18,17,14,15,-19,18,15,16,-20,21,20,19,-17,20,21,22,-24,23,22,24,-26,25,24,26,-30,29,26,27,-31,30,27,28,-32,31,28,5,-7,9,2,10,-12,0,1,13,-13,3,4,5,28,27,26,24,22,21,16,15,14,13,-2,6,7,8,9,11,17,18,19,20,23,25,29,30,-32
		} 
		Edges: *50 {
			a: 0,1,13,9,5,4,6,7,10,11,3,15,2,16,17,18,19,21,25,29,22,23,26,27,30,31,33,32,35,37,39,38,41,43,42,45,49,53,57,46,47,50,51,54,55,59,61,63,65,67
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *288 {
				a: 0,0.00623759115114808,0.999980568885803,0,1.12859297018986e-07,1,0,1.12859297018986e-07,1,0,0.00623759115114808,0.999980568885803,-5.67076563484648e-09,0.707105815410614,0.707107722759247,-5.67076563484648e-09,0.707105815410614,0.707107722759247,0,0.499958962202072,0.86604917049408,0,0.499958962202072,0.86604917049408,0,0.499958962202072,0.86604917049408,0,0.499958962202072,0.86604917049408,0,0.258864432573318,0.965913653373718,0,0.258864432573318,0.965913653373718,0,0.258864432573318,0.965913653373718,0,0.258864432573318,0.965913653373718,0,0.00623759115114808,0.999980568885803,0,0.00623759115114808,0.999980568885803,0,0.00623740209266543,-0.999980568885803,0,-8.46444550006709e-08,-1,0,-8.46444550006709e-08,-1,0,0.00623740209266543,-0.999980568885803,0,0.00623740209266543,-0.999980568885803,0,0.00623740209266543,-0.999980568885803,0,0.258864939212799,-0.965913474559784,0,0.258864939212799,-0.965913474559784,0,0.258864939212799,-0.965913474559784,0,0.258864939212799,-0.965913474559784,0,0.499958962202072,-0.866049110889435,0,0.499958962202072,-0.866049110889435,0,0.499958962202072,-0.866049110889435,0,0.499958962202072,-0.866049110889435,0,0.707105457782745,-0.707108020782471,0,0.707105457782745,-0.707108020782471,2.83389933741773e-09,0.866048216819763,-0.499960452318192,2.83389933741773e-09,0.866048216819763,-0.499960452318192,0,0.707105457782745,-0.707108020782471,0,0.707105457782745,-0.707108020782471,2.83389933741773e-09,0.866048216819763,-0.499960452318192,2.83389933741773e-09,0.866048216819763,-0.499960452318192,3.5423757260844e-09,0.965913414955139,-0.258865267038345,3.5423757260844e-09,0.965913414955139,-0.258865237236023,3.5423757260844e-09,0.965913414955139,-0.258865237236023,3.5423757260844e-09,0.965913414955139,-0.258865267038345,1.7521382955632e-10,0.999867737293243,-0.016265407204628,1.7521382955632e-10,0.999867737293243,-0.016265407204628,1.7521382955632e-10,0.999867737293243,-0.016265407204628,1.7521382955632e-10,0.999867737293243,-0.016265407204628,0,0.999867737293243,0.016265407204628,
0,0.999867737293243,0.016265407204628,0,0.999867737293243,0.016265407204628,0,0.999867737293243,0.016265407204628,-5.66779645438942e-09,0.965913474559784,0.258865028619766,-5.66779645438942e-09,0.965913474559784,0.258865028619766,-5.66779645438942e-09,0.965913474559784,0.258865028619766,-5.66779645438942e-09,0.965913474559784,0.258865028619766,-1.13355893560652e-08,0.866048514842987,0.499960035085678,-1.13355884678867e-08,0.866048514842987,0.499960035085678,-1.13355884678867e-08,0.866048514842987,0.499960035085678,-1.13355893560652e-08,0.866048514842987,0.499960035085678,-5.67076563484648e-09,0.707105815410614,0.707107722759247,-5.67076563484648e-09,0.707105815410614,0.707107722759247,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1.26610075312783e-07,0,-1,-1.26610089523638e-07,0,-1,-1.26610089523638e-07,0,-0.999999940395355,-1.26610075312783e-07,0,-1,-1.26610075312783e-07,0,-1,-1.26610075312783e-07,0,-1,-1.26610075312783e-07,0,-1,-1.26610089523638e-07,0,-0.999999940395355,-1.26610075312783e-07,0,-1,-1.26610089523638e-07,0,-1,-1.26610075312783e-07,0,-1,-1.26610075312783e-07,0,-1,-1.26610075312783e-07,0,-1,-1.26610075312783e-07,0,1,-7.72987846175965e-07,0,0.999999940395355,-7.72987789332547e-07,0,1,-7.72987846175965e-07,0,1,-7.72987903019384e-07,0,1,-7.72987903019384e-07,0,1,-7.72987846175965e-07,0,0.999999940395355,-7.72987846175965e-07,0,0.999999940395355,-7.72987789332547e-07,0,1,-7.72987903019384e-07,0,1,-7.72987789332547e-07,0,1,-7.72987903019384e-07,0,1,-7.72987903019384e-07,0,1,-7.72987732489128e-07,0,1,-7.72987789332547e-07,0
			} 
			NormalsW: *96 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *72 {
				a: 0.40966796875,0.96728515625,0.59033203125,0.96728515625,0.40966796875,0.78271484375,0.59912109375,0.96728515625,0.607421875,0.96728515625,0.625,0.96728515625,0.625,0.78271484375,0.607421875,0.78271484375,0.59912109375,0.78271484375,0.59033203125,0.78271484375,0.40966796875,0.467041015625,0.59033203125,0.467041015625,0.40966796875,0.282958984375,0.59033203125,0.282958984375,0.59912109375,0.282958984375,0.607421875,0.282958984375,0.625,0.282958984375,0.59912109375,0.467041015625,0.607421875,0.467041015625,0.625,0.467041015625,0.65771484375,0.53271484375,0.65771484375,0.5,0.84228515625,0.53271484375,0.84228515625,0.5,0.84228515625,0.54833984375,0.65771484375,0.54833984375,0.84228515625,0.5654296875,0.65771484375,0.5654296875,0.84228515625,0.6845703125,0.84228515625,0.70166015625,0.84228515625,0.71728515625,0.84228515625,0.75,0.65771484375,0.6845703125,0.65771484375,0.70166015625,0.65771484375,0.71728515625,0.65771484375,0.75
			} 
			UVIndex: *96 {
				a: 1,0,2,9,6,5,4,7,7,4,3,8,8,3,1,9,11,10,12,13,11,13,14,17,17,14,15,18,18,15,16,19,22,20,21,23,20,22,24,25,25,24,26,27,27,26,28,32,32,28,29,33,33,29,30,34,34,30,31,35,9,2,10,11,0,1,13,12,3,4,31,30,29,28,26,24,22,16,15,14,13,1,6,7,8,9,11,17,18,21,20,25,27,32,33,34
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2477304099728, "Model::Gate_1", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A",0,-182.255411148071,1.19209289550781e-05
			P: "currentUVSet", "KString", "", "U", "UVSet0"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2478585271312, "Material::Default_Material1", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	AnimationStack: 2479025569616, "AnimStack::Take 001", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",1924423250
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",1924423250
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 2478585161072, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Gate_1, Model::RootNode
	C: "OO",2477304099728,0
	
	;AnimLayer::BaseLayer, AnimStack::Take 001
	C: "OO",2478585161072,2479025569616
	
	;Geometry::, Model::Gate_1
	C: "OO",2479089013616,2477304099728
	
	;Material::Default_Material1, Model::Gate_1
	C: "OO",2478585271312,2477304099728
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "Take 001"
	Take: "Take 001" {
		FileName: "Take_001.tak"
		LocalTime: 1924423250,230930790000
		ReferenceTime: 1924423250,230930790000
	}
}
