; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 13
		Hour: 11
		Minute: 53
		Second: 2
		Millisecond: 628
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate\Mesh_gate_2.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate\Mesh_gate_2.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "13/03/2025 04:53:02.627"
			P: "Original|FileName", "KString", "", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate\Mesh_gate_2.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "13/03/2025 04:53:02.627"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Project Unity\Block-Color\Assets\BlockColor\SelfDev\Meshes\_NewModel\Gate"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Front"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2478592035040, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "Take 001"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2479089017360, "Geometry::", "Mesh" {
		Vertices: *96 {
			a: -200,96.4861297607422,49.8475112915039,-200,231.688049316406,49.8474998474121,199.999984741211,96.4861297607422,49.8475074768066,-200,238.414978027344,48.9622039794922,-200,244.690155029297,46.3629379272461,-200,250.072814941406,42.2322731018066,199.999984741211,250.072814941406,42.2322692871094,199.999984741211,244.690155029297,46.3629379272461,199.999984741211,238.414978027344,48.9622001647949,199.999984741211,231.688049316406,49.8474960327148,199.999984741211,96.4861297607422,-49.8475227355957,199.999984741211,231.688049316406,-49.8475341796875,-200,96.4861297607422,-49.8475151062012,-200,231.688049316406,-49.847526550293,-200,238.414978027344,-48.962230682373,-200,244.690155029297,-46.3629760742188,-200,250.072814941406,-42.2323036193848,199.999984741211,238.414978027344,-48.9622383117676,199.999984741211,244.690155029297,-46.362979888916,199.999984741211,250.072814941406,-42.232307434082,199.999984741211,254.203491210938,-36.8496437072754,-200,254.203491210938,-36.8496437072754,-200,256.802734375,-30.5744743347168,199.999984741211,256.802734375,-30.5744743347168,-200,257.688049316406,-23.8475322723389,199.999984741211,257.688049316406,-23.8475341796875,-200,257.688049316406,23.8474941253662,-200,256.802734375,30.5744342803955,-200,254.203491210938,36.849609375,199.999984741211,257.688049316406,23.8474941253662,199.999984741211,256.802734375,30.5744342803955,199.999984741211,254.203491210938,36.849609375
		} 
		PolygonVertexIndex: *96 {
			a: 1,0,2,-10,6,5,4,-8,7,4,3,-9,8,3,1,-10,11,10,12,-14,11,13,14,-18,17,14,15,-19,18,15,16,-20,21,20,19,-17,20,21,22,-24,23,22,24,-26,25,24,26,-30,29,26,27,-31,30,27,28,-32,31,28,5,-7,9,2,10,-12,0,1,13,-13,3,4,5,28,27,26,24,22,21,16,15,14,13,-2,6,7,8,9,11,17,18,19,20,23,25,29,30,-32
		} 
		Edges: *50 {
			a: 0,1,13,9,5,4,6,7,10,11,3,15,2,16,17,18,19,21,25,29,22,23,26,27,30,31,33,32,35,37,39,38,41,43,42,45,49,53,57,46,47,50,51,54,55,59,61,63,65,67
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *288 {
				a: 1.07508058011518e-09,0.00623756321147084,0.999980568885803,0,8.46444550006709e-08,1,0,8.46444550006709e-08,1,1.07508058011518e-09,0.00623756321147084,0.999980568885803,2.83538681422613e-09,0.707106292247772,0.707107245922089,2.83538681422613e-09,0.707106292247772,0.707107245922089,8.50170067678846e-09,0.499959737062454,0.866048574447632,8.50170067678846e-09,0.499959737062454,0.866048574447632,8.50170067678846e-09,0.499959737062454,0.866048574447632,8.50170067678846e-09,0.499959737062454,0.866048574447632,1.70034013535769e-08,0.258864432573318,0.965913593769073,1.70034013535769e-08,0.258864432573318,0.965913593769073,1.70034013535769e-08,0.258864432573318,0.965913593769073,1.70034013535769e-08,0.258864432573318,0.965913593769073,1.07508058011518e-09,0.00623756321147084,0.999980568885803,1.07508058011518e-09,0.00623756321147084,0.999980568885803,-1.82763688627574e-08,0.00623740162700415,-0.999980568885803,-1.80574843966497e-08,-8.46444550006709e-08,-1,-1.80574843966497e-08,-8.46444550006709e-08,-1,-1.82763688627574e-08,0.00623740162700415,-0.999980568885803,-1.82763688627574e-08,0.00623740162700415,-0.999980568885803,-1.82763688627574e-08,0.00623740162700415,-0.999980568885803,-1.70034049062906e-08,0.258863925933838,-0.965913772583008,-1.70034049062906e-08,0.258863925933838,-0.965913772583008,-1.70034049062906e-08,0.258863925933838,-0.965913772583008,-1.70034049062906e-08,0.258863925933838,-0.965913772583008,-1.41695011279808e-08,0.499959647655487,-0.866048634052277,-1.41695011279808e-08,0.499959647655487,-0.866048634052277,-1.41695011279808e-08,0.499959647655487,-0.866048634052277,-1.41695011279808e-08,0.499959647655487,-0.866048634052277,-1.13415445923692e-08,0.707106530666351,-0.70710700750351,-1.13415445923692e-08,0.707106530666351,-0.70710700750351,-2.83390133581918e-09,0.8660489320755,-0.499959290027618,-2.83390133581918e-09,0.8660489320755,-0.499959290027618,-1.13415445923692e-08,0.707106530666351,-0.70710700750351,-1.13415445923692e-08,0.707106530666351,-0.70710700750351,-2.83390133581918e-09,0.8660489320755,-0.499959290027618,
-2.83390133581918e-09,0.8660489320755,-0.499959290027618,-3.54237555955095e-10,0.965913772583008,-0.258863776922226,-3.54237555955095e-10,0.965913772583008,-0.258863776922226,-3.54237555955095e-10,0.965913772583008,-0.258863776922226,-3.54237555955095e-10,0.965913772583008,-0.258863776922226,-8.76069355948417e-11,0.999867737293243,-0.0162654090672731,-8.76069355948417e-11,0.999867737293243,-0.0162654090672731,-8.76069355948417e-11,0.999867737293243,-0.0162654090672731,-8.76069355948417e-11,0.999867737293243,-0.0162654090672731,0,0.999867737293243,0.016265407204628,0,0.999867737293243,0.016265407204628,0,0.999867737293243,0.016265407204628,0,0.999867737293243,0.016265407204628,0,0.965913832187653,0.258863717317581,0,0.965913832187653,0.258863717317581,0,0.965913832187653,0.258863717317581,0,0.965913832187653,0.258863717317581,0,0.866048991680145,0.499959170818329,0,0.866048991680145,0.499959170818329,0,0.866048991680145,0.499959170818329,0,0.866048991680145,0.499959170818329,2.83538681422613e-09,0.707106292247772,0.707107245922089,2.83538681422613e-09,0.707106292247772,0.707107245922089,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.999999940395355,-2.53220207468985e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220235890694e-07,0,-0.999999940395355,-2.53220207468985e-07,0,-0.999999940395355,-2.53220207468985e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220264312404e-07,0,-1,-2.53220235890694e-07,0,-1,-2.53220235890694e-07,0,1,-2.66547630189962e-07,0,1,-2.66547630189962e-07,0,1,-2.66547658611671e-07,0,1,-2.66547630189962e-07,0,1,-2.66547630189962e-07,0,1,-2.66547630189962e-07,0,1,-2.66547630189962e-07,0,1,-2.66547658611671e-07,0,0.999999940395355,-2.66547601768252e-07,0,0.999999940395355,-2.66547630189962e-07,0,1,-2.66547630189962e-07,0,1,-2.66547630189962e-07,0,1,-2.66547601768252e-07,0,0.999999940395355,-2.66547630189962e-07,0
			} 
			NormalsW: *96 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *72 {
				a: 0.40966796875,0.98388671875,0.59033203125,0.98388671875,0.40966796875,0.76611328125,0.59912109375,0.98388671875,0.607421875,0.98388671875,0.625,0.98388671875,0.625,0.76611328125,0.607421875,0.76611328125,0.59912109375,0.76611328125,0.59033203125,0.76611328125,0.40966796875,0.483642578125,0.59033203125,0.483642578125,0.40966796875,0.266357421875,0.59033203125,0.266357421875,0.59912109375,0.266357421875,0.607421875,0.266357421875,0.625,0.266357421875,0.59912109375,0.483642578125,0.607421875,0.483642578125,0.625,0.483642578125,0.64111328125,0.53271484375,0.64111328125,0.5,0.85888671875,0.53271484375,0.85888671875,0.5,0.85888671875,0.54833984375,0.64111328125,0.54833984375,0.85888671875,0.5654296875,0.64111328125,0.5654296875,0.85888671875,0.6845703125,0.85888671875,0.70166015625,0.85888671875,0.71728515625,0.85888671875,0.75,0.64111328125,0.6845703125,0.64111328125,0.70166015625,0.64111328125,0.71728515625,0.64111328125,0.75
			} 
			UVIndex: *96 {
				a: 1,0,2,9,6,5,4,7,7,4,3,8,8,3,1,9,11,10,12,13,11,13,14,17,17,14,15,18,18,15,16,19,22,20,21,23,20,22,24,25,25,24,26,27,27,26,28,32,32,28,29,33,33,29,30,34,34,30,31,35,9,2,10,11,0,1,13,12,3,4,31,30,29,28,26,24,22,16,15,14,13,1,6,7,8,9,11,17,18,21,20,25,27,32,33,34
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2478051737632, "Model::Gate_2", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A",0,-182.255411148071,1.19209289550781e-05
			P: "currentUVSet", "KString", "", "U", "UVSet0"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2478585266112, "Material::Default_Material1", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	AnimationStack: 2479025537792, "AnimStack::Take 001", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",1924423250
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",1924423250
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 2478585150736, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Gate_2, Model::RootNode
	C: "OO",2478051737632,0
	
	;AnimLayer::BaseLayer, AnimStack::Take 001
	C: "OO",2478585150736,2479025537792
	
	;Geometry::, Model::Gate_2
	C: "OO",2479089017360,2478051737632
	
	;Material::Default_Material1, Model::Gate_2
	C: "OO",2478585266112,2478051737632
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "Take 001"
	Take: "Take 001" {
		FileName: "Take_001.tak"
		LocalTime: 1924423250,230930790000
		ReferenceTime: 1924423250,230930790000
	}
}
