import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GridManager')
export class GridManager extends Component {
    static _instance: GridManager;
    static get instance() {
        return this._instance;
    }
    @property([Node])
    public listGrid : Node[] = [];
    public gridNodes : Node[][] = [];
    @property
    private gridSizeCol : number = 6;
    @property
    private gridSizeRow : number = 9;


    onLoad(){
        GridManager._instance = this;
        this.InitData();
    }
    InitData(){
        this.node.children.forEach((item) => {
            this.listGrid.push(item);
        })
        for (let i = 0; i < this.gridSizeRow; i++) {
            let row: Node[] = []; // Tạo hàng mới cho mảng 2 chiều

            for (let j = 0; j < this.gridSizeCol; j++) {
                const index = i * this.gridSizeCol + j;
                row.push(this.listGrid[index]);
            }

            this.gridNodes.push(row);
        }
        console.log(this.gridNodes);
    }
    FindNodePosition(target: Node): { row: number, col: number } | null {
        for (let i = 0; i < this.gridNodes.length; i++) {
            for (let j = 0; j < this.gridNodes[i].length; j++) {
                if (this.gridNodes[i][j] === target) {
                    return { row: i, col: j };
                }
            }
        }
        return null; // nếu không tìm thấy
    }
}


