; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 20
		Hour: 17
		Minute: 28
		Second: 26
		Millisecond: 198
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side3.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side3.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:28:26.197"
			P: "Original|FileName", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side3.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:28:26.197"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle"
			P: "Original|ApplicationNativeFile", "KString", "", "", "C:\Users\<USER>\Desktop\Outsouces\object.ma"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2065082068000, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "object"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2065351771664, "Geometry::", "Mesh" {
		Vertices: *138 {
			a: -300,84.8572158813477,-50.3455047607422,-300,-97.7000045776367,-50.3455047607422,300,-97.7000045776367,-50.3457412719727,300,84.8572158813477,-50.3457412719727,-300,-97.7000045776367,50.3457412719727,300,-97.7000045776367,50.3455047607422,300,97.6999969482422,-37.5029602050781,300,97.6999969482422,50.3455047607422,-300,97.6999969482422,50.3457412719727,-300,97.6999969482422,-37.5027198791504,-300,91.2786102294922,-48.6248893737793,-300,95.9793930053711,-43.9241065979004,300,95.8234786987305,-49.8476104736328,300,231.025390625,-49.8476104736328,-300,95.8234786987305,-49.8474273681641,300,237.752319335938,-48.9623146057129,300,244.027496337891,-46.3630485534668,300,249.41015625,-42.2323837280273,-300,249.41015625,-42.2322006225586,-300,244.027496337891,-46.3628692626953,-300,237.752319335938,-48.9621315002441,-300,231.025390625,-49.8474273681641,-300,95.8234786987305,49.8476104736328,-300,231.025390625,49.8476104736328,300,95.8234786987305,49.8474273681641,300,231.025390625,49.8474273681641,300,237.752319335938,48.9621315002441,300,244.027496337891,46.3628692626953,300,249.41015625,42.2322006225586,-300,237.752319335938,48.9623146057129,-300,244.027496337891,46.3630485534668,-300,249.41015625,42.2323837280273,300,91.2786102294922,-48.6251335144043,300,95.9793930053711,-43.9243507385254,-300,253.540832519531,36.8497161865234,300,253.540832519531,36.849536895752,300,256.140075683594,30.5743598937988,-300,256.140075683594,30.5745429992676,300,257.025390625,23.8474197387695,-300,257.025390625,23.8476028442383,300,257.025390625,-23.8476028442383,300,256.140075683594,-30.5745449066162,300,253.540832519531,-36.8497161865234,-300,257.025390625,-23.8474197387695,-300,256.140075683594,-30.5743637084961,-300,253.540832519531,-36.849536895752
		} 
		PolygonVertexIndex: *146 {
			a: 2,1,0,-4,4,1,2,-6,8,7,6,-10,1,4,8,-1,10,0,8,-12,11,8,-10,4,5,7,-9,13,12,14,-22,18,17,16,-20,19,16,15,-21,20,15,13,-22,23,22,24,-26,23,25,26,-30,29,26,27,-31,30,27,28,-32,10,32,3,-1,11,33,32,-11,9,6,33,-12,5,2,3,-8,7,3,32,-34,7,33,-7,35,34,31,-29,34,35,36,-38,37,36,38,-40,39,38,40,-44,43,40,41,-45,44,41,42,-46,45,42,17,-19,21,14,22,-24,12,13,25,-25,15,16,17,42,41,40,38,36,35,28,27,26,25,-14,18,19,20,21,23,29,30,31,34,37,39,43,44,-46
		} 
		Edges: *75 {
			a: 0,1,2,3,4,6,7,8,9,10,11,13,14,16,18,19,22,24,27,28,29,30,31,32,33,34,36,37,38,40,42,43,44,45,46,48,49,50,52,53,54,56,57,58,59,60,63,64,68,73,78,82,83,85,87,88,89,91,92,93,95,96,97,99,100,101,103,104,105,107,109,111,113,115,117
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *438 {
				a: -3.89318984161946e-07,0,-1,-3.89318984161946e-07,0,-1,-3.89318984161946e-07,0,-1,-3.89318984161946e-07,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,3.48209354683604e-08,0,1,3.48209354683604e-08,0,1,3.48209354683604e-08,0,1,3.48209354683604e-08,-1,0,6.06161506766512e-07,-1,0,6.06161506766512e-07,-1,-3.53235418693032e-09,6.06238131695136e-07,-1,-3.06748013656488e-08,6.06818275628029e-07,-1,-1.61295758971391e-06,6.40699113318988e-07,-1,-3.06748013656488e-08,6.06818275628029e-07,-1,-3.53235418693032e-09,6.06238131695136e-07,-1,-1.63628362770396e-06,6.41479118712596e-07,-1,-1.63628362770396e-06,6.41479118712596e-07,-1,-3.53235418693032e-09,6.06238131695136e-07,-1,-3.2303903481079e-06,6.947809652047e-07,3.88530537520637e-07,0,1,3.88530537520637e-07,0,1,3.88530537520637e-07,0,1,3.88530537520637e-07,0,1,-3.00371425510093e-07,0.00623748172074556,-0.999980568885803,-2.99950016824369e-07,0,-1,-2.99950016824369e-07,0,-1,-3.00371425510093e-07,0.00623748218640685,-0.999980568885803,-2.10999701266701e-07,0.707106590270996,-0.707106947898865,-2.10999715477556e-07,0.707106530666351,-0.707107126712799,-2.61728160921848e-07,0.499959200620651,-0.8660489320755,-2.61728132500139e-07,0.499959170818329,-0.8660489320755,-2.61728132500139e-07,0.499959170818329,-0.8660489320755,-2.61728160921848e-07,0.499959200620651,-0.8660489320755,-2.97504328727882e-07,0.258864462375641,-0.965913653373718,-2.97504328727882e-07,0.258864492177963,-0.965913653373718,-2.97504328727882e-07,0.258864492177963,-0.965913653373718,-2.97504328727882e-07,0.258864462375641,-0.965913653373718,-3.00371425510093e-07,0.00623748172074556,-0.999980568885803,-3.00371425510093e-07,0.00623748218640685,-0.999980568885803,3.00371425510093e-07,0.00623748172074556,0.999980568885803,2.99950016824369e-07,0,1,2.99950016824369e-07,0,1,3.00371425510093e-07,0.00623748218640685,0.999980568885803,3.00371425510093e-07,0.00623748172074556,0.999980568885803,3.00371425510093e-07,0.00623748218640685,0.999980568885803,2.97504328727882e-07,0.258864492177963,0.965913653373718,2.97504328727882e-07,0.258864462375641,0.965913653373718,
2.97504328727882e-07,0.258864462375641,0.965913653373718,2.97504328727882e-07,0.258864492177963,0.965913653373718,2.61728132500139e-07,0.499959170818329,0.8660489320755,2.61728160921848e-07,0.499959200620651,0.8660489320755,2.61728160921848e-07,0.499959200620651,0.8660489320755,2.61728132500139e-07,0.499959170818329,0.8660489320755,2.10999701266701e-07,0.707106590270996,0.707106947898865,2.10999715477556e-07,0.707106530666351,0.707107126712799,-3.79652618676118e-07,0.258819669485092,-0.965925633907318,-3.79652590254409e-07,0.258819699287415,-0.965925633907318,-3.79652618676118e-07,0.258819669485092,-0.965925633907318,-3.79652618676118e-07,0.258819669485092,-0.965925633907318,-2.84739570588499e-07,0.707106471061707,-0.707107126712799,-2.84739599010209e-07,0.707106530666351,-0.707107126712799,-2.84739570588499e-07,0.707106471061707,-0.707107126712799,-2.84739599010209e-07,0.707106530666351,-0.707107126712799,-1.02506312771311e-07,0.965926051139832,-0.25881814956665,-1.02506319876738e-07,0.965926051139832,-0.258818179368973,-1.02506312771311e-07,0.965926051139832,-0.25881814956665,-1.02506319876738e-07,0.965926051139832,-0.258818179368973,1,0,-6.06161506766512e-07,1,0,-6.06161506766512e-07,1,7.0113898686941e-08,-6.06818332471448e-07,1,7.70414310125034e-09,-6.06238131695136e-07,1,7.70414310125034e-09,-6.06238131695136e-07,1,7.0113898686941e-08,-6.06818332471448e-07,1,3.68675978279498e-06,-6.40699056475569e-07,1,3.63359004040831e-06,-6.41478948182339e-07,1,7.70414310125034e-09,-6.06238131695136e-07,1,3.63359004040831e-06,-6.41478948182339e-07,1,0,-6.94780908361281e-07,1.49693661910533e-07,0.866049587726593,0.499958217144012,1.49693647699678e-07,0.866049528121948,0.499958217144012,2.10999715477556e-07,0.707106530666351,0.707107126712799,2.10999701266701e-07,0.707106590270996,0.707106947898865,1.49693647699678e-07,0.866049528121948,0.499958217144012,1.49693661910533e-07,0.866049587726593,0.499958217144012,7.74358497324101e-08,0.965914070606232,0.258862882852554,7.74358568378375e-08,0.965914070606232,0.258862942457199,7.74358568378375e-08,0.965914070606232,0.258862942457199,
7.74358497324101e-08,0.965914070606232,0.258862882852554,4.9477626440364e-09,0.999867796897888,0.016265282407403,4.94776619675008e-09,0.999867737293243,0.0162652917206287,4.94776619675008e-09,0.999867737293243,0.0162652917206287,4.9477626440364e-09,0.999867796897888,0.016265282407403,-4.88955542721214e-09,0.999867737293243,-0.0162652898579836,-4.88955276267689e-09,0.999867737293243,-0.0162652805447578,-4.88955276267689e-09,0.999867737293243,-0.0162652805447578,-4.88955542721214e-09,0.999867737293243,-0.0162652898579836,-7.72004824511896e-08,0.965914070606232,-0.258862942457199,-7.72004611349075e-08,0.965914130210876,-0.258862882852554,-7.72004611349075e-08,0.965914130210876,-0.258862882852554,-7.72004824511896e-08,0.965914070606232,-0.258862942457199,-1.49693690332242e-07,0.866049528121948,-0.499958366155624,-1.49693676121387e-07,0.866049528121948,-0.49995830655098,-1.49693676121387e-07,0.866049528121948,-0.49995830655098,-1.49693690332242e-07,0.866049528121948,-0.499958366155624,-2.10999715477556e-07,0.707106530666351,-0.707107126712799,-2.10999701266701e-07,0.707106590270996,-0.707106947898865,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,9.06261959698895e-07,0,1,9.06261846012058e-07,0,1,9.06261959698895e-07,0,1,9.06261902855476e-07,0,1,9.06261959698895e-07,0,1,9.06261902855476e-07,0,1,9.06261902855476e-07,0,1,9.06261902855476e-07,0,1,9.06261902855476e-07,0,1,9.06262016542314e-07,0,1,9.06262016542314e-07,0,1,9.06261959698895e-07,0,1,9.06261902855476e-07,0,1,9.06261902855476e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404837576993e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404837576993e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404723890155e-07,0,-1,-5.86404780733574e-07,0,-1,-5.86404780733574e-07,0
			} 
			NormalsW: *146 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *100 {
				a: 0.388363063335419,0.196676462888718,0.388362467288971,0.196676462888718,0.388362467288971,0.339486598968506,0.388363063335419,0.339486598968506,0.110351502895355,0.196676462888718,0.110352098941803,0.196676462888718,0.110351502895355,0.349533170461655,0.110352098941803,0.349533170461655,0.352903842926025,0.349533170461655,0.352903246879578,0.349533170461655,0.383611857891083,0.344509899616241,0.370632886886597,0.348187237977982,0.897283554077148,0.365058928728104,0.386987686157227,0.348065286874771,0.386987268924713,0.348065286874771,0.386987268924713,0.453830450773239,0.877362906932831,0.378684669733047,0.877363264560699,0.378684669733047,0.888168513774872,0.374695360660553,0.888168096542358,0.374695360660553,0.89496773481369,0.370044589042664,0.894967257976532,0.370044589042664,0.636497259140015,0.365058928728104,0.111726850271225,0.348065286874771,0.111727297306061,0.348065286874771,0.111727297306061,0.453830450773239,0.638813436031342,0.370044589042664,0.638813078403473,0.370044589042664,0.645612716674805,0.374695360660553,0.645612299442291,0.374695360660553,0.656417906284332,0.378684669733047,0.656417548656464,0.378684669733047,0.383612453937531,0.344509899616241,0.370633453130722,0.348187237977982,0.670498073101044,0.381746083498001,0.67049765586853,0.381746083498001,0.686912894248962,0.383672446012497,0.686912477016449,0.383672446012497,0.704509437084198,0.384328663349152,0.704509019851685,0.384328663349152,0.829271793365479,0.384328663349152,0.82927143573761,0.384328663349152,0.846868336200714,0.383672446012497,0.84686803817749,0.383672446012497,0.863283157348633,0.381746083498001,0.863282799720764,0.381746083498001,0.386987686157227,0.453830450773239,0.636497795581818,0.365058928728104,0.111726850271225,0.453830450773239,0.897283017635345,0.365058928728104
			} 
			UVIndex: *146 {
				a: 0,1,2,3,4,1,0,5,6,7,8,9,1,4,6,2,10,2,6,11,11,6,9,4,5,7,6,46,13,14,15,16,17,18,19,19,18,20,21,21,20,12,49,48,23,24,25,22,47,26,27,27,26,28,29,29,28,30,31,10,32,3,2,11,33,32,10,9,8,33,11,5,0,3,7,7,3,32,33,7,33,8,34,35,31,30,35,34,36,37,37,36,38,39,39,38,40,41,41,40,42,43,43,42,44,45,45,44,17,16,15,14,23,48,13,46,25,24,20,18,17,44,42,40,38,36,34,30,28,26,47,12,16,19,21,49,22,27,29,31,35,37,39,41,43,45
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2065197370896, "Model::Side3", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A",0,-79.994026184082,0
			P: "currentUVSet", "KString", "", "U", "UVSet0"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2065008558000, "Material::Default_Material4", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	AnimationStack: 2064725994240, "AnimStack::object", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",1924423250
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",1924423250
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 2065074434864, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Side3, Model::RootNode
	C: "OO",2065197370896,0
	
	;AnimLayer::BaseLayer, AnimStack::object
	C: "OO",2065074434864,2064725994240
	
	;Geometry::, Model::Side3
	C: "OO",2065351771664,2065197370896
	
	;Material::Default_Material4, Model::Side3
	C: "OO",2065008558000,2065197370896
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "object"
	Take: "object" {
		FileName: "object.tak"
		LocalTime: 1924423250,230930790000
		ReferenceTime: 1924423250,230930790000
	}
}
