import { _decorator, Component, Node, tween, Vec3 } from 'cc';
import {BlockControl} from "db://assets/Asset/Src/Core/BlockControl";
import {GameControl} from "db://assets/Asset/Src/Core/GameControl";
const { ccclass, property } = _decorator;

@ccclass('ControlUI')
export class ControlUI extends Component {
    @property(Node)
    dumb : Node = null;
    @property(Node)
    genius : Node = null;
    average : number = 0;
    @property(Node)
    playNow : Node = null;
    @property(Node)
    popUpWin : Node = null;
    @property(Node)
    textTut : Node = null;
    @property(Node)
    hand : Node = null;
    @property(Node)
    introFirst : Node = null;
    static _instance : ControlUI;
    static get instance() {
        return this._instance;
    }
    onLoad(){
        ControlUI._instance = this;
        this.DistanceAVE();
        this.IntroShowIcon();
    }
    DistanceAVE(){
        this.average  = Vec3.distance(new Vec3(this.dumb.worldPosition.x,0,0), new Vec3(this.genius.worldPosition.x,0,0))/BlockControl.instance.totalBlocks;
    }
    public TweenDumb(){
        let target = this.dumb.worldPosition.add(new Vec3(this.average,0,0)).clone();
        tween(this.dumb)
            .to(0.5,{worldPosition : target},{easing : "smooth"})
            .start();
    }
    ShowCTAWin(){
        this.popUpWin.active = true;
        this.playNow.active = false;
    }
    IntroShowIcon(){
        this.scheduleOnce(()=>{
            this.introFirst.active = false;
            GameControl.instance.checkCanTouch = true;
        },1.5)
    }
}


