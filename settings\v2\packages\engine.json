{"__version__": "1.0.9", "modules": {"cache": {"base": {"_value": true}, "gfx-webgl": {"_value": true}, "gfx-webgl2": {"_value": false}, "animation": {"_value": true}, "skeletal-animation": {"_value": false}, "3d": {"_value": true}, "meshopt": {"_value": true}, "2d": {"_value": true}, "xr": {"_value": false}, "ui": {"_value": true}, "particle": {"_value": true}, "physics": {"_value": true, "_option": "physics-ammo"}, "physics-ammo": {"_value": true}, "physics-cannon": {"_value": false}, "physics-physx": {"_value": false}, "physics-builtin": {"_value": false}, "physics-2d": {"_value": false, "_option": "physics-2d-box2d"}, "physics-2d-box2d": {"_value": true}, "physics-2d-builtin": {"_value": false}, "intersection-2d": {"_value": false}, "primitive": {"_value": true}, "profiler": {"_value": true}, "occlusion-query": {"_value": false}, "geometry-renderer": {"_value": false}, "debug-renderer": {"_value": false}, "particle-2d": {"_value": true}, "audio": {"_value": true}, "video": {"_value": false}, "webview": {"_value": true}, "tween": {"_value": true}, "websocket": {"_value": false}, "websocket-server": {"_value": false}, "terrain": {"_value": false}, "light-probe": {"_value": false}, "tiled-map": {"_value": false}, "spine": {"_value": false}, "dragon-bones": {"_value": false}, "marionette": {"_value": false}, "procedural-animation": {"_value": false}, "custom-pipeline": {"_value": false}}, "flags": {}, "includeModules": ["2d", "3d", "animation", "audio", "base", "gfx-webgl", "meshopt", "particle", "particle-2d", "physics-ammo", "primitive", "profiler", "tween", "ui", "webview"], "noDeprecatedFeatures": {"value": false, "version": ""}}}