import { _decorator, Component, Node } from 'cc';
import {ControlUI} from "db://assets/Asset/Src/Core/ControlUI";
const { ccclass, property } = _decorator;

@ccclass('BlockControl')
export class BlockControl extends Component {
    @property([Node])
    listBlock : Node[] = [];
    public totalBlocks : number = 0;
    static _instance : BlockControl;
    static get instance(){
        return this._instance;
    }
    onLoad(){
        BlockControl._instance = this;
        this.Init()
    }
    Init(){
        this.node.children.forEach(child =>{
            this.listBlock.push(child);
            this.totalBlocks++;
        })
    }
    RemoveBlockInList(block : Node){
        let index = this.listBlock.indexOf(block);
        if (index > -1){
            this.listBlock.splice(index, 1);
        }
    }
    CheckNoMoreBlock(): boolean{
        return this.listBlock.length == 0;
    }
}


