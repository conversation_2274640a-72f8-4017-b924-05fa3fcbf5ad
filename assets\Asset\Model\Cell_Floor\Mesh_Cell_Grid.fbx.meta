{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6", "files": [], "subMetas": {"abef7": {"importer": "gltf-mesh", "uuid": "77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6@abef7", "displayName": "", "id": "abef7", "name": "Mesh_grid.mesh", "userData": {"gltfIndex": 0, "triangleCount": 18}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "fd884": {"importer": "gltf-material", "uuid": "77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6@fd884", "displayName": "", "id": "fd884", "name": "Mat_grid.material", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}, "d37ca": {"importer": "gltf-scene", "uuid": "77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6@d37ca", "displayName": "", "id": "d37ca", "name": "Mesh_Cell_Grid.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": ["77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6@abef7"], "skeletons": [], "textures": [], "materials": ["77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6@fd884"], "scenes": ["77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6@d37ca"]}, "materials": {"77e4806d-6b2b-49a6-9cbe-ec6e1c2d00a6@fd884": {"__type__": "cc.Material", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_effectAsset": {"__uuid__": "c8f66d17-351a-48da-a12c-0212d28575c4", "__expectedType__": "cc.EffectAsset"}, "_techIdx": 0, "_defines": [{}, {}, {}, {}, {}, {}], "_states": [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], "_props": [{"mainColor": {"__type__": "cc.Color", "r": 18, "g": 25, "b": 94, "a": 255}}, {}, {}, {}, {}, {}]}}, "meshCompress": {"enable": true}}}