import { _decorator, Button, Component, Node } from 'cc';
import gameEndHandler from "db://assets/PLAGameFoundation/gameControl/utilities/handler/gameEndHandler";

const { ccclass, property } = _decorator;

@ccclass('ButtonLinkStore')
export class ButtonLinkStore extends Component {
    @property(Button)
    btnChangeLink: Button = null;

    protected onLoad(): void {
        this.btnChangeLink.node.on('click',this.OnChangeLink,this);
    }

    OnChangeLink(){
        gameEndHandler.StoreGameChangingNoCondition();
    }
}