import { _decorator, BoxCollider, Component, ICollisionEvent, Node, tween, Vec3 } from 'cc';
import {TypeColor, TypeDirection} from "db://assets/Asset/Src/Utils/Enum";
const { ccclass, property } = _decorator;

@ccclass('Wall')
export class Wall extends Component {
    public nodeDetect : Node = null;
    @property
    public typeColor : TypeColor = 1;
    @property
    public dirType : TypeDirection = 1;
    public bus : Node = null;
    @property(Node)
    effect: Node = null;

    start() {
        this.effect.active = false;
        const collider = this.node.getComponent(BoxCollider);
        if (collider) {
            // collider.on('onCollisionEnter', this.onCollisionEnter, this);
            // collider.on('onCollisionExit', this.onCollisionExit, this);
        }
    }

    onCollisionEnter(event: ICollisionEvent) {
        // console.log('Va chạm với:', event.otherCollider.node.name);
    }
    onCollisionExit(event : ICollisionEvent){
        // console.log('<PERSON><PERSON><PERSON> thúc va chạm với:', event.otherCollider.node.name);
    }
    WallDown(){
        tween(this.node)
            .to(0.3,{worldPosition : new Vec3(this.node.worldPosition.add(new Vec3(0,-1.5,0)))})
            .start();
    }
    WallUp(){
        tween(this.node)
            .to(0.3,{worldPosition : new Vec3(this.node.worldPosition.add(new Vec3(0,1.5,0)))})
            .start();
    }
}


