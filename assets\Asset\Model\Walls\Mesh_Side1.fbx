; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 20
		Hour: 17
		Minute: 28
		Second: 20
		Millisecond: 363
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side1.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side1.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:28:20.363"
			P: "Original|FileName", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_Side1.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:28:20.363"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle"
			P: "Original|ApplicationNativeFile", "KString", "", "", "C:\Users\<USER>\Desktop\Outsouces\object.ma"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2065361724272, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "object"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2065351758352, "Geometry::", "Mesh" {
		Vertices: *138 {
			a: -100,84.8572158813477,-50.3456230163574,-100,-97.7000045776367,-50.3456230163574,100,-97.7000045776367,-50.3456230163574,100,84.8572158813477,-50.3456230163574,-100,-97.7000045776367,50.3456230163574,100,-97.7000045776367,50.3456230163574,-100,97.6999969482422,50.3456230163574,-100,97.6999969482422,-37.5028419494629,100,97.6999969482422,-37.5028419494629,100,97.6999969482422,50.3456230163574,100,91.2786102294922,-48.6250114440918,100,95.9794006347656,-43.9242286682129,-100,96.3901519775391,49.8475227355957,-100,231.592041015625,49.8475227355957,100,96.3901519775391,49.8475227355957,-100,238.318969726563,48.9622268676758,-100,244.594146728516,46.362964630127,-100,249.976806640625,42.2322959899902,100,249.976806640625,42.2322959899902,100,244.594146728516,46.362964630127,100,238.318969726563,48.9622268676758,100,231.592041015625,49.8475227355957,100,96.3901519775391,-49.8475151062012,100,231.592041015625,-49.8475151062012,-100,96.3901519775391,-49.8475151062012,-100,231.592041015625,-49.8475151062012,-100,238.318969726563,-48.9622192382813,-100,244.594146728516,-46.3629531860352,-100,249.976806640625,-42.2322883605957,100,238.318969726563,-48.9622192382813,100,244.594146728516,-46.3629531860352,100,249.976806640625,-42.2322883605957,-100,91.2786102294922,-48.6250114440918,-100,95.9794006347656,-43.9242286682129,100,254.107482910156,-36.8496208190918,-100,254.107482910156,-36.8496246337891,-100,256.706756591797,-30.5744533538818,100,256.706756591797,-30.5744495391846,-100,257.592071533203,-23.8475151062012,100,257.592071533203,-23.8475151062012,-100,257.592071533203,23.8475151062012,-100,256.706756591797,30.5744552612305,-100,254.107482910156,36.8496284484863,100,257.592071533203,23.8475151062012,100,256.706756591797,30.5744552612305,100,254.107482910156,36.8496322631836
		} 
		PolygonVertexIndex: *146 {
			a: 2,1,0,-4,2,5,4,-2,8,7,6,-10,4,5,9,-7,5,2,3,-10,9,3,10,-12,9,11,-9,13,12,14,-22,18,17,16,-20,19,16,15,-21,20,15,13,-22,23,22,24,-26,23,25,26,-30,29,26,27,-31,30,27,28,-32,32,10,3,-1,33,11,10,-33,7,8,11,-34,1,4,6,-1,32,0,6,-34,33,6,-8,35,34,31,-29,34,35,36,-38,37,36,38,-40,39,38,40,-44,43,40,41,-45,44,41,42,-46,45,42,17,-19,21,14,22,-24,12,13,25,-25,15,16,17,42,41,40,38,36,35,28,27,26,25,-14,18,19,20,21,23,29,30,31,34,37,39,43,44,-46
		} 
		Edges: *75 {
			a: 0,1,2,3,4,5,6,8,9,10,11,13,15,18,21,22,23,25,27,28,29,30,31,32,33,34,36,37,38,40,42,43,44,45,46,48,49,50,52,53,54,56,57,58,59,62,63,66,70,73,77,82,83,85,87,88,89,91,92,93,95,96,97,99,100,101,103,104,105,107,109,111,113,115,117
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *438 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,6.47163878042534e-10,0,1,6.47163878042534e-10,0,1,0,0,1,0,0,1,1.86349623731985e-07,0,1,6.47163878042534e-10,0,1,1.86349623731985e-07,0,1,1.29216159621137e-05,0,0,0.006237484049052,0.999980628490448,0,0,1,0,0,1,0,0.006237484049052,0.999980628490448,0,0.707106828689575,0.707106709480286,0,0.707106828689575,0.707106709480286,0,0.49995955824852,0.866048812866211,0,0.49995955824852,0.866048812866211,0,0.49995955824852,0.866048812866211,0,0.49995955824852,0.866048812866211,0,0.258864432573318,0.965913712978363,0,0.258864432573318,0.965913712978363,0,0.258864432573318,0.965913712978363,0,0.258864432573318,0.965913712978363,0,0.006237484049052,0.999980628490448,0,0.006237484049052,0.999980628490448,0,0.006237484049052,-0.999980628490448,0,0,-1,0,0,-1,0,0.006237484049052,-0.999980628490448,0,0.006237484049052,-0.999980628490448,0,0.006237484049052,-0.999980628490448,0,0.258864670991898,-0.965913593769073,0,0.258864670991898,-0.965913593769073,0,0.258864670991898,-0.965913593769073,0,0.258864670991898,-0.965913593769073,0,0.49995955824852,-0.866048812866211,0,0.49995955824852,-0.866048812866211,0,0.49995955824852,-0.866048812866211,0,0.49995955824852,-0.866048812866211,0,0.707106709480286,-0.707106828689575,0,0.707106709480286,-0.707106828689575,0,0.258819669485092,-0.965925753116608,0,0.258819669485092,-0.965925753116608,0,0.258819669485092,-0.965925753116608,0,0.258819669485092,-0.965925753116608,0,0.707105994224548,-0.707107543945313,0,0.707105994224548,-0.707107543945313,0,0.707105994224548,-0.707107543945313,0,0.707105994224548,-0.707107543945313,0,0.965926468372345,-0.258816808462143,0,0.965926468372345,-0.258816808462143,0,0.965926468372345,-0.258816808462143,0,0.965926468372345,-0.258816808462143,-1,0,0,-1,0,0,-1,7.65609131647693e-10,0,-1,3.28658722459352e-09,0,-1,1.72816768895245e-07,0,-1,3.28658722459352e-09,0,-1,7.65609131647693e-10,0,-1,2.86793010673136e-07,0,-1,2.86793010673136e-07,0,
-1,7.65609131647693e-10,0,-1,8.07601099950261e-06,0,2.86341128585832e-09,0.866047918796539,-0.499960988759995,2.86341195199213e-09,0.866047978401184,-0.499961048364639,0,0.707106709480286,-0.707106828689575,0,0.707106709480286,-0.707106828689575,2.86341195199213e-09,0.866047978401184,-0.499961048364639,2.86341128585832e-09,0.866047918796539,-0.499960988759995,2.86341395039358e-09,0.965913116931915,-0.258866548538208,2.86341350630437e-09,0.965913116931915,-0.258866518735886,2.86341350630437e-09,0.965913116931915,-0.258866518735886,2.86341395039358e-09,0.965913116931915,-0.258866548538208,0,0.999867737293243,-0.0162654854357243,0,0.999867737293243,-0.0162654854357243,0,0.999867737293243,-0.0162654854357243,0,0.999867737293243,-0.0162654854357243,0,0.999867737293243,0.0162654854357243,0,0.999867737293243,0.0162654854357243,0,0.999867737293243,0.0162654854357243,0,0.999867737293243,0.0162654854357243,0,0.965913116931915,0.258866399526596,0,0.965913116931915,0.258866399526596,0,0.965913116931915,0.258866399526596,0,0.965913116931915,0.258866399526596,0,0.866048038005829,0.499960869550705,0,0.866048097610474,0.499960869550705,0,0.866048097610474,0.499960869550705,0,0.866048038005829,0.499960869550705,0,0.707106828689575,0.707106709480286,0,0.707106828689575,0.707106709480286,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1.83251316343558e-07,0,-1,-1.83251330554413e-07,0,-1,-1.83251287921848e-07,0,-1,-1.83251344765267e-07,0,-1,-1.83251330554413e-07,0,-1,-1.83251330554413e-07,0,-1,-1.83251330554413e-07,0,-1,-1.83251287921848e-07,0,-1,-1.83251330554413e-07,0,-1,-1.83251316343558e-07,0,-1,-1.83251316343558e-07,0,-1,-1.83251330554413e-07,0,-1,-1.83251330554413e-07,0,-1,-1.83251330554413e-07,0,1,2.1323789667349e-07,0,1,2.1323789667349e-07,0,1,2.13237910884345e-07,0,1,2.1323789667349e-07,0,1,2.1323789667349e-07,0,1,2.132379250952e-07,0,1,2.1323789667349e-07,0,1,2.1323789667349e-07,0,1,2.13237910884345e-07,0,1,2.13237910884345e-07,0,1,2.1323789667349e-07,0,1,2.1323789667349e-07,0,1,2.13237882462636e-07,0,1,2.13237910884345e-07,0
			} 
			NormalsW: *146 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *100 {
				a: 0.388362765312195,0.196676462888718,0.388362765312195,0.196676462888718,0.388362765312195,0.339486598968506,0.388362765312195,0.339486598968506,0.110351786017418,0.196676462888718,0.110351786017418,0.196676462888718,0.352903544902802,0.349533170461655,0.352903544902802,0.349533170461655,0.110351786017418,0.349533170461655,0.110351786017418,0.349533170461655,0.383612155914307,0.344509899616241,0.370633184909821,0.348187237977982,0.636497557163239,0.365478992462158,0.111727073788643,0.348508536815643,0.111727058887482,0.348508536815643,0.111727058887482,0.454273790121078,0.656417667865753,0.379104614257813,0.656417667865753,0.379104614257813,0.64561253786087,0.375115305185318,0.64561253786087,0.375115305185318,0.638813316822052,0.370464563369751,0.638813257217407,0.370464563369751,0.897283315658569,0.365478992462158,0.386987507343292,0.348508536815643,0.386987507343292,0.348508536815643,0.386987507343292,0.454273790121078,0.894967555999756,0.370464563369751,0.894967555999756,0.370464563369751,0.888168275356293,0.375115305185318,0.888168275356293,0.375115305185318,0.87736314535141,0.379104614257813,0.87736314535141,0.379104614257813,0.383612155914307,0.344509899616241,0.370633184909821,0.348187237977982,0.863283038139343,0.382165998220444,0.863283038139343,0.382165998220444,0.846868216991425,0.384092509746552,0.846868216991425,0.384092509746552,0.829271614551544,0.384748607873917,0.829271614551544,0.384748607873917,0.704509198665619,0.384748607873917,0.704509198665619,0.384748607873917,0.686912715435028,0.384092509746552,0.686912715435028,0.384092509746552,0.670497834682465,0.382165998220444,0.67049777507782,0.382165998220444,0.111727073788643,0.454273790121078,0.897283315658569,0.365478992462158,0.386987507343292,0.454273790121078,0.636497497558594,0.365478992462158
			} 
			UVIndex: *146 {
				a: 0,1,2,3,0,4,5,1,6,7,8,9,5,4,9,8,4,0,3,9,9,3,10,11,9,11,6,46,13,14,15,16,17,18,19,19,18,20,21,21,20,12,49,48,23,24,25,22,47,26,27,27,26,28,29,29,28,30,31,32,10,3,2,33,11,10,32,7,6,11,33,1,5,8,2,32,2,8,33,33,8,7,34,35,31,30,35,34,36,37,37,36,38,39,39,38,40,41,41,40,42,43,43,42,44,45,45,44,17,16,15,14,23,48,13,46,25,24,20,18,17,44,42,40,38,36,34,30,28,26,47,12,16,19,21,49,22,27,29,31,35,37,39,41,43,45
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2065197412656, "Model::Side1", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A",0,-79.994026184082,0
			P: "currentUVSet", "KString", "", "U", "UVSet0"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2064895881824, "Material::Default_Material2", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	AnimationStack: 2065352714464, "AnimStack::object", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",1924423250
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",1924423250
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 2065074435168, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Side1, Model::RootNode
	C: "OO",2065197412656,0
	
	;AnimLayer::BaseLayer, AnimStack::object
	C: "OO",2065074435168,2065352714464
	
	;Geometry::, Model::Side1
	C: "OO",2065351758352,2065197412656
	
	;Material::Default_Material2, Model::Side1
	C: "OO",2064895881824,2065197412656
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "object"
	Take: "object" {
		FileName: "object.tak"
		LocalTime: 1924423250,230930790000
		ReferenceTime: 1924423250,230930790000
	}
}
