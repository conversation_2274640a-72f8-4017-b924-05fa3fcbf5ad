; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 20
		Hour: 17
		Minute: 30
		Second: 51
		Millisecond: 4
	}
	Creator: "FBX SDK/FBX Plugins version 2020.1"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_block_gate_corner.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_block_gate_corner.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2022"
			P: "Original|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:30:51.003"
			P: "Original|FileName", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle\Mesh_block_gate_corner.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2022"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "20/03/2025 10:30:51.003"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Project Unity\Block-Color_2\Assets\BlockColor\SelfDev\Meshes\_NewModel\Obstacle"
			P: "Original|ApplicationNativeFile", "KString", "", "", "C:\Users\<USER>\Desktop\Outsouces\object.ma"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",**********
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2065354806368, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "object"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2065072502000, "Geometry::", "Mesh" {
		Vertices: *324 {
			a: 50.3474273681641,16.4921035766602,-50.3455390930176,42.2322998046875,170.078765869141,-50.3455390930176,46.3629608154297,164.696105957031,-50.3455390930176,48.9622344970703,158.420928955078,-50.3455390930176,50.3474273681641,151.694000244141,-50.3455390930176,-48.9622192382813,158.420928955078,-50.3455390930176,-46.3629684448242,164.696105957031,-50.3455467224121,-42.2322845458984,170.078765869141,-50.3455390930176,-36.8496246337891,174.209442138672,-50.3455390930176,-30.5744476318359,176.808715820313,-50.3455390930176,-23.8475189208984,177.694030761719,-50.3455390930176,23.8475189208984,177.694030761719,-50.3418617248535,30.5744476318359,176.808715820313,-50.3455390930176,36.8496246337891,174.209442138672,-50.3455390930176,-50.3474082946777,151.694000244141,49.8475227355957,-50.3474159240723,158.420928955078,48.9622192382813,-50.3474082946777,164.696105957031,46.362964630127,-50.3474159240723,170.078765869141,42.2322807312012,-50.3474159240723,158.420928955078,-48.9622192382813,-50.3474311828613,164.696105957031,-46.3629608154297,-50.3474159240723,170.078765869141,-42.2322998046875,-50.3474159240723,174.209442138672,-36.8496246337891,-50.3474006652832,176.808715820313,-30.5744609832764,-50.3474159240723,177.694030761719,-23.8475189208984,-50.3436622619629,177.694030761719,23.8475151062012,-50.3474159240723,176.808715820313,30.5744476318359,-50.3474082946777,174.209442138672,36.8496284484863,-36.8475341796875,177.694030761719,-36.8475189208984,-50.3474082946777,16.4920959472656,49.8475227355957,-23.8400650024414,16.4920978546143,49.8475074768066,-9.46432495117188,16.4920978546143,48.4316253662109,4.35894775390625,16.4920978546143,44.2383880615234,17.0985565185547,16.4920997619629,37.4289245605469,28.2649230957031,16.4920997619629,28.2649230957031,37.4289245605469,16.4920997619629,17.0985546112061,44.2383880615234,16.4921016693115,4.35894632339478,48.4316253662109,16.4921016693115,-9.46432590484619,49.8475189208984,16.4921016693115,-23.8400726318359,49.8475189208984,151.694000244141,-23.8400726318359,
48.4316253662109,151.694000244141,-9.46432590484619,44.2383880615234,151.694000244141,4.35894632339478,37.4289245605469,151.694000244141,17.0985546112061,28.2649230957031,151.694000244141,28.2649230957031,17.0985565185547,151.694000244141,37.4289245605469,4.35894775390625,151.694000244141,44.2383880615234,-9.46432495117188,151.694000244141,48.4316253662109,-23.8400650024414,151.694000244141,49.8475074768066,48.9622344970703,158.420928955078,-24.7253684997559,47.5463409423828,158.420928955078,-10.3496398925781,43.3531036376953,158.420928955078,3.47364950180054,36.5436401367188,158.420928955078,16.2132549285889,27.379638671875,158.420928955078,27.3796234130859,16.2132873535156,158.420928955078,36.5436248779297,3.47367858886719,158.420928955078,43.3530883789063,-10.349609375,158.420928955078,47.5463409423828,-24.725341796875,158.420928955078,48.9622192382813,46.3629608154297,164.696105957031,-27.3246002197266,44.9470672607422,164.696105957031,-12.9488849639893,40.7538299560547,164.696105957031,0.874406218528748,33.9443511962891,164.696105957031,13.614013671875,24.7803649902344,164.696105957031,24.7803802490234,13.6139984130859,164.696105957031,33.9443664550781,0.874374389648438,164.696105957031,40.7538299560547,-12.9489135742188,164.696105957031,44.9470672607422,-27.3246536254883,164.696105957031,46.3629493713379,42.2322998046875,170.078765869141,-31.4553241729736,40.8164215087891,170.078765869141,-17.0795917510986,36.6231689453125,170.078765869141,-3.25628805160522,29.8137054443359,170.078765869141,9.48332214355469,20.6497192382813,170.078765869141,20.6496887207031,9.48335266113281,170.078765869141,29.8136901855469,-3.25625610351563,170.078765869141,36.6231536865234,-17.0795288085938,170.078765869141,40.81640625,-31.4552612304688,170.078765869141,42.2322807312012,-43.1131591796875,176.808715820313,30.5744476318359,-28.737419128418,176.808715820313,29.158561706543,-14.9141387939453,176.808715820313,24.9653148651123,-2.17451477050781,176.808715820313,18.1558532714844,8.99185180664063,176.808715820313,8.99186706542969,
18.1558532714844,176.808715820313,-2.17449951171875,24.9653167724609,176.808715820313,-14.9141092300415,29.1585540771484,176.808715820313,-28.7373809814453,30.5744476318359,176.808715820313,-43.1131134033203,22.431640625,177.694030761719,-35.4643859863281,18.2383880615234,177.694030761719,-21.6410980224609,11.4289245605469,177.694030761719,-8.90147304534912,2.26493835449219,177.694030761719,2.264892578125,-8.90141296386719,177.694030761719,11.4289093017578,-21.6410064697266,177.694030761719,18.2383728027344,-35.4642791748047,177.694030761719,22.4316272735596,-36.8379974365234,174.209442138672,36.8496284484863,-22.4622573852539,174.209442138672,35.4337387084961,-8.63896179199219,174.209442138672,31.2404937744141,4.10064697265625,174.209442138672,24.4310302734375,15.2670135498047,174.209442138672,15.2670440673828,24.4310150146484,174.209442138672,4.10067844390869,31.2404937744141,174.209442138672,-8.63891506195068,35.4337310791016,174.209442138672,-22.4622039794922,36.8496246337891,174.209442138672,-36.8379249572754,-43.8338012695313,177.694030761719,23.2557392120361,23.2557220458984,177.694030761719,-43.8337554931641,-40.2109832763672,176.808715820313,-40.2109832763672,-49.4048843383789,158.420928955078,-49.4048690795898,-43.3485870361328,174.209442138672,-43.3485717773438,-46.0399169921875,170.078765869141,-46.0399017333984,-48.1052398681641,164.696105957031,-48.105224609375,-50.3474159240723,16.4920959472656,-50.3455543518066,-50.3474159240723,151.694000244141,-50.3455543518066
		} 
		PolygonVertexIndex: *394 {
			a: 7,104,103,-9,9,101,27,-11,102,18,19,-106,24,99,27,-24,27,100,11,-11,37,38,39,-37,36,39,40,-36,35,40,41,-35,34,41,42,-34,33,42,43,-33,32,43,44,-32,31,44,45,-31,30,45,46,-30,82,12,11,-101,81,83,84,-81,80,84,85,-80,79,85,86,-79,78,86,87,-78,77,87,88,-77,76,88,89,-76,75,89,99,-75,1,65,56,-3,2,56,47,-4,3,47,38,-5,4,38,37,-1,55,15,14,-47,64,16,15,-56,73,17,16,-65,74,25,26,-91,12,82,98,-14,90,26,17,-74,13,98,65,-2,14,28,29,-47,27,84,-84,27,85,-85,27,86,-86,27,87,-87,27,88,-88,27,89,-89,27,99,-90,45,54,55,-47,44,53,54,-46,43,52,53,-45,42,51,52,-44,41,50,51,-43,40,49,50,-42,38,47,48,-40,39,48,49,-41,54,63,64,-56,53,62,63,-55,52,61,62,-54,51,60,61,-53,50,59,60,-52,49,58,59,-51,47,56,57,-49,48,57,58,-50,63,72,73,-65,62,71,72,-64,61,70,71,-63,60,69,70,-62,59,68,69,-61,58,67,68,-60,56,65,66,-58,57,66,67,-59,81,97,98,-83,80,96,97,-82,79,95,96,-81,78,94,95,-80,77,93,94,-79,76,92,93,-78,74,90,91,-76,75,91,92,-77,65,98,97,-67,66,97,96,-68,67,96,95,-69,68,95,94,-70,69,94,93,-71,70,93,92,-72,71,92,91,-73,72,91,90,-74,24,25,74,-100,82,100,83,-82,27,83,-101,101,22,23,-28,104,20,21,-104,21,22,101,-104,103,101,9,-9,5,102,105,-7,7,6,105,-105,104,105,19,-21,5,107,-103,102,107,-19,5,6,7,8,9,10,11,12,13,1,2,3,4,-108,18,107,14,15,16,17,26,25,24,23,22,21,20,-20,14,107,106,-29,0,106,107,-5
		} 
		Edges: *203 {
			a: 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,21,22,23,25,26,27,29,30,31,33,34,35,37,38,39,41,42,43,45,46,47,49,50,51,52,53,55,56,57,58,59,61,62,63,65,66,67,69,70,71,73,74,75,77,78,79,81,82,83,84,85,86,87,89,90,91,93,94,95,98,99,100,101,102,103,104,105,107,108,109,111,112,113,114,115,117,118,119,121,123,125,127,128,129,132,134,135,138,141,144,147,153,154,157,158,161,162,165,166,169,170,173,174,178,179,182,185,186,189,190,193,194,197,198,201,202,205,206,210,211,214,217,218,221,222,225,226,229,230,233,234,237,238,242,243,246,249,250,252,253,254,257,258,261,262,265,266,269,270,274,275,278,283,287,291,295,299,303,307,313,318,324,325,328,329,330,332,334,338,340,342,343,344,346,350,352,353,356,370,373,387,388,390
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1182 {
				a: -0.572739124298096,0.640501916408539,-0.511592924594879,-0.555457770824432,0.618699967861176,-0.555587112903595,-0.423170030117035,0.801125824451447,-0.423231303691864,-0.408871620893478,0.829161584377289,-0.381202340126038,-0.185100913047791,0.966834664344788,-0.175978735089302,-0.22731252014637,0.946914672851563,-0.227336302399635,-0.019389109686017,0.999623954296112,-0.0193913634866476,-0.00669874297454953,0.999957025051117,-0.00641875900328159,-0.673198580741882,0.304879009723663,-0.673685789108276,-0.539624691009521,0.30023118853569,-0.786553621292114,-0.560156166553497,0.445885211229324,-0.698148608207703,-0.636143743991852,0.436216980218887,-0.63642430305481,0.000515904044732451,0.999928116798401,0.0119819957762957,0.00134799932129681,0.999848008155823,0.0173837449401617,-0.019389109686017,0.999623954296112,-0.0193913634866476,-0.00641589239239693,0.999957025051117,-0.00669931014999747,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.017384011298418,0.999848067760468,0.00134668650571257,0.0119814639911056,0.999928176403046,0.000513891456648707,-0.00669874297454953,0.999957025051117,-0.00641875900328159,0.998903810977936,0,0.0468112789094448,0.998882353305817,0.00729607976973057,0.0466994382441044,0.980700373649597,0.00729882158339024,0.195380628108978,0.980785369873047,0,0.195090264081955,0.980785369873047,0,0.195090264081955,0.980700373649597,0.00729882158339024,0.195380628108978,0.923770010471344,0.00808213371783495,0.382862389087677,0.923879563808441,0,0.382683277130127,0.923879563808441,0,0.382683277130127,0.923770010471344,0.00808213371783495,0.382862389087677,0.831380426883698,0.00856289453804493,0.555637776851654,0.831469595432281,0,0.555570304393768,0.831469595432281,0,0.555570304393768,0.831380426883698,0.00856289453804493,0.555637776851654,0.707079887390137,0.00872492976486683,0.707079827785492,0.707106828689575,0,0.70710676908493,0.707106828689575,0,0.70710676908493,0.707079887390137,0.00872492976486683,0.707079827785492,0.555637776851654,0.00856286287307739,0.831380426883698,
0.555570304393768,0,0.831469595432281,0.555570304393768,0,0.831469595432281,0.555637776851654,0.00856286287307739,0.831380426883698,0.382862359285355,0.00808213092386723,0.923770070075989,0.382683277130127,0,0.923879683017731,0.382683277130127,0,0.923879683017731,0.382862359285355,0.00808213092386723,0.923770070075989,0.195380240678787,0.00729883788153529,0.980700492858887,0.195089906454086,0,0.980785369873047,0.195089906454086,0,0.980785369873047,0.195380240678787,0.00729883788153529,0.980700492858887,0.0347944423556328,0.00624322285875678,0.999374985694885,0.0346121154725552,0,0.999400854110718,0.263400882482529,0.964617669582367,0.0115275662392378,0.279658585786819,0.960096955299377,0.00221690302714705,0.0119814639911056,0.999928176403046,0.000513891456648707,0.017384011298418,0.999848067760468,0.00134668650571257,0.228022545576096,0.972760498523712,0.0417478829622269,0.0283790454268456,0.999585092067719,0.00493711978197098,0.0244520474225283,0.999656140804291,0.00947369076311588,0.192134395241737,0.978334486484528,0.0771116241812706,0.192134395241737,0.978334486484528,0.0771116241812706,0.0244520474225283,0.999656140804291,0.00947369076311588,0.0218863785266876,0.999658763408661,0.0142614375799894,0.163214698433876,0.980688393115997,0.107755362987518,0.163214698433876,0.980688393115997,0.107755362987518,0.0218863785266876,0.999658763408661,0.0142614375799894,0.0184479299932718,0.999659597873688,0.0184477940201759,0.135753512382507,0.981397986412048,0.135753720998764,0.135753512382507,0.981397986412048,0.135753720998764,0.0184479299932718,0.999659597873688,0.0184477940201759,0.0142615288496017,0.999658763408661,0.0218861196190119,0.107755593955517,0.980688393115997,0.163214772939682,0.107755593955517,0.980688393115997,0.163214772939682,0.0142615288496017,0.999658763408661,0.0218861196190119,0.00947361998260021,0.999656140804291,0.0244519934058189,0.0771115943789482,0.978334546089172,0.192134112119675,0.0771115943789482,0.978334546089172,0.192134112119675,0.00947361998260021,0.999656140804291,0.0244519934058189,
0.00493707042187452,0.999585092067719,0.0283790659159422,0.0417474992573261,0.972760379314423,0.228022918105125,0.0417474992573261,0.972760379314423,0.228022918105125,0.00493707042187452,0.999585092067719,0.0283790659159422,0.00134799932129681,0.999848008155823,0.0173837449401617,0.0115262707695365,0.964617311954498,0.263402193784714,0.71891850233078,0.695094406604767,0,0.703433334827423,0.710178911685944,0.028768515214324,0.864279687404633,0.501871168613434,0.0338521972298622,0.870899438858032,0.491461366415024,0,0.870899438858032,0.491461366415024,0,0.864279687404633,0.501871168613434,0.0338521972298622,0.961341440677643,0.272342443466187,0.0406495444476604,0.962116479873657,0.27259424328804,0.00493302429094911,0.962116479873657,0.27259424328804,0.00493302429094911,0.961341440677643,0.272342443466187,0.0406495444476604,0.998882353305817,0.00729607976973057,0.0466994382441044,0.999799251556396,0.0078809317201376,0.0184254981577396,0.999799251556396,0.0078809317201376,0.0184254981577396,0.998882353305817,0.00729607976973057,0.0466994382441044,0.998903810977936,0,0.0468112789094448,0.999822199344635,0,0.0188572220504284,0.0373258255422115,0.259676456451416,0.964974105358124,1.79977732273073e-07,0.254463911056519,0.967082321643829,5.27669840266753e-07,0.00613819342106581,0.999981164932251,0.0347944423556328,0.00624322285875678,0.999374985694885,0.0338500924408436,0.501869261264801,0.864280939102173,2.50322898409649e-07,0.491461962461472,0.870899021625519,1.79977732273073e-07,0.254463911056519,0.967082321643829,0.0373258255422115,0.259676456451416,0.964974105358124,0.028767392039299,0.710177183151245,0.703435003757477,1.83107843554353e-07,0.695095360279083,0.718917548656464,2.50322898409649e-07,0.491461962461472,0.870899021625519,0.0338500924408436,0.501869261264801,0.864280939102173,0.0115262707695365,0.964617311954498,0.263402193784714,0.0022163656540215,0.960098564624786,0.279653251171112,0,0.851250171661377,0.524760067462921,0.0218902062624693,0.869662165641785,0.493161767721176,0.279658585786819,0.960096955299377,0.00221690302714705,
0.263400882482529,0.964617669582367,0.0115275662392378,0.493157476186752,0.86966460943222,0.0218917690217495,0.524762690067291,0.851248681545258,0,0.0218902062624693,0.869662165641785,0.493161767721176,0,0.851250171661377,0.524760067462921,1.83107843554353e-07,0.695095360279083,0.718917548656464,0.028767392039299,0.710177183151245,0.703435003757477,0.524762690067291,0.851248681545258,0,0.493157476186752,0.86966460943222,0.0218917690217495,0.703433334827423,0.710178911685944,0.028768515214324,0.71891850233078,0.695094406604767,0,5.27669840266753e-07,0.00613819342106581,0.999981164932251,5.44980593986111e-07,0,1,0.0346121154725552,0,0.999400854110718,0.0347944423556328,0.00624322285875678,0.999374985694885,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.0244520474225283,0.999656140804291,0.00947369076311588,0.0283790454268456,0.999585092067719,0.00493711978197098,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.0218863785266876,0.999658763408661,0.0142614375799894,0.0244520474225283,0.999656140804291,0.00947369076311588,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.0184479299932718,0.999659597873688,0.0184477940201759,0.0218863785266876,0.999658763408661,0.0142614375799894,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.0142615288496017,0.999658763408661,0.0218861196190119,0.0184479299932718,0.999659597873688,0.0184477940201759,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.00947361998260021,0.999656140804291,0.0244519934058189,0.0142615288496017,0.999658763408661,0.0218861196190119,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.00493707042187452,0.999585092067719,0.0283790659159422,0.00947361998260021,0.999656140804291,0.0244519934058189,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.00134799932129681,0.999848008155823,0.0173837449401617,0.00493707042187452,0.999585092067719,0.0283790659159422,0.195380240678787,0.00729883788153529,0.980700492858887,0.19109158217907,0.300028294324875,0.934594631195068,0.0373258255422115,0.259676456451416,0.964974105358124,
0.0347944423556328,0.00624322285875678,0.999374985694885,0.382862359285355,0.00808213092386723,0.923770070075989,0.364643961191177,0.328308075666428,0.871348738670349,0.19109158217907,0.300028294324875,0.934594631195068,0.195380240678787,0.00729883788153529,0.980700492858887,0.555637776851654,0.00856286287307739,0.831380426883698,0.522849321365356,0.345231294631958,0.7793869972229,0.364643961191177,0.328308075666428,0.871348738670349,0.382862359285355,0.00808213092386723,0.923770070075989,0.707079887390137,0.00872492976486683,0.707079827785492,0.662154912948608,0.350860357284546,0.662154018878937,0.522849321365356,0.345231294631958,0.7793869972229,0.555637776851654,0.00856286287307739,0.831380426883698,0.831380426883698,0.00856289453804493,0.555637776851654,0.779387176036835,0.345231413841248,0.522848844528198,0.662154912948608,0.350860357284546,0.662154018878937,0.707079887390137,0.00872492976486683,0.707079827785492,0.923770010471344,0.00808213371783495,0.382862389087677,0.871348977088928,0.328307628631592,0.364643812179565,0.779387176036835,0.345231413841248,0.522848844528198,0.831380426883698,0.00856289453804493,0.555637776851654,0.998882353305817,0.00729607976973057,0.0466994382441044,0.961341440677643,0.272342443466187,0.0406495444476604,0.934594869613647,0.30002760887146,0.191091403365135,0.980700373649597,0.00729882158339024,0.195380628108978,0.980700373649597,0.00729882158339024,0.195380628108978,0.934594869613647,0.30002760887146,0.191091403365135,0.871348977088928,0.328307628631592,0.364643812179565,0.923770010471344,0.00808213371783495,0.382862389087677,0.19109158217907,0.300028294324875,0.934594631195068,0.163741111755371,0.559973299503326,0.812169194221497,0.0338500924408436,0.501869261264801,0.864280939102173,0.0373258255422115,0.259676456451416,0.964974105358124,0.364643961191177,0.328308075666428,0.871348738670349,0.307741403579712,0.598826289176941,0.73939323425293,0.163741111755371,0.559973299503326,0.812169194221497,0.19109158217907,0.300028294324875,0.934594631195068,0.522849321365356,0.345231294631958,0.7793869972229,
0.436099916696548,0.620794832706451,0.651483476161957,0.307741403579712,0.598826289176941,0.73939323425293,0.364643961191177,0.328308075666428,0.871348738670349,0.662154912948608,0.350860357284546,0.662154018878937,0.550341129302979,0.627893030643463,0.550340950489044,0.436099916696548,0.620794832706451,0.651483476161957,0.522849321365356,0.345231294631958,0.7793869972229,0.779387176036835,0.345231413841248,0.522848844528198,0.651483476161957,0.620794713497162,0.436100095510483,0.550341129302979,0.627893030643463,0.550340950489044,0.662154912948608,0.350860357284546,0.662154018878937,0.871348977088928,0.328307628631592,0.364643812179565,0.739393293857574,0.598826110363007,0.307741701602936,0.651483476161957,0.620794713497162,0.436100095510483,0.779387176036835,0.345231413841248,0.522848844528198,0.961341440677643,0.272342443466187,0.0406495444476604,0.864279687404633,0.501871168613434,0.0338521972298622,0.812168955802917,0.559973418712616,0.163741737604141,0.934594869613647,0.30002760887146,0.191091403365135,0.934594869613647,0.30002760887146,0.191091403365135,0.812168955802917,0.559973418712616,0.163741737604141,0.739393293857574,0.598826110363007,0.307741701602936,0.871348977088928,0.328307628631592,0.364643812179565,0.163741111755371,0.559973299503326,0.812169194221497,0.126318827271461,0.759221851825714,0.638455867767334,0.028767392039299,0.710177183151245,0.703435003757477,0.0338500924408436,0.501869261264801,0.864280939102173,0.307741403579712,0.598826289176941,0.73939323425293,0.233784645795822,0.790824830532074,0.565633296966553,0.126318827271461,0.759221851825714,0.638455867767334,0.163741111755371,0.559973299503326,0.812169194221497,0.436099916696548,0.620794832706451,0.651483476161957,0.327443152666092,0.807591140270233,0.490487068891525,0.233784645795822,0.790824830532074,0.565633296966553,0.307741403579712,0.598826289176941,0.73939323425293,0.550341129302979,0.627893030643463,0.550340950489044,0.411876559257507,0.812844395637512,0.411876022815704,0.327443152666092,0.807591140270233,0.490487068891525,0.436099916696548,0.620794832706451,0.651483476161957,
0.651483476161957,0.620794713497162,0.436100095510483,0.490487515926361,0.807591021060944,0.32744288444519,0.411876559257507,0.812844395637512,0.411876022815704,0.550341129302979,0.627893030643463,0.550340950489044,0.739393293857574,0.598826110363007,0.307741701602936,0.565633177757263,0.790824770927429,0.233784794807434,0.490487515926361,0.807591021060944,0.32744288444519,0.651483476161957,0.620794713497162,0.436100095510483,0.864279687404633,0.501871168613434,0.0338521972298622,0.703433334827423,0.710178911685944,0.028768515214324,0.638455867767334,0.759221792221069,0.126318693161011,0.812168955802917,0.559973418712616,0.163741737604141,0.812168955802917,0.559973418712616,0.163741737604141,0.638455867767334,0.759221792221069,0.126318693161011,0.565633177757263,0.790824770927429,0.233784794807434,0.739393293857574,0.598826110363007,0.307741701602936,0.228022545576096,0.972760498523712,0.0417478829622269,0.437655448913574,0.895176827907562,0.0843575596809387,0.493157476186752,0.86966460943222,0.0218917690217495,0.263400882482529,0.964617669582367,0.0115275662392378,0.192134395241737,0.978334486484528,0.0771116241812706,0.379056185483932,0.912262976169586,0.155218631029129,0.437655448913574,0.895176827907562,0.0843575596809387,0.228022545576096,0.972760498523712,0.0417478829622269,0.163214698433876,0.980688393115997,0.107755362987518,0.324617087841034,0.920864701271057,0.215944111347198,0.379056185483932,0.912262976169586,0.155218631029129,0.192134395241737,0.978334486484528,0.0771116241812706,0.135753512382507,0.981397986412048,0.135753720998764,0.271251231431961,0.92349636554718,0.271251767873764,0.324617087841034,0.920864701271057,0.215944111347198,0.163214698433876,0.980688393115997,0.107755362987518,0.107755593955517,0.980688393115997,0.163214772939682,0.215943649411201,0.920864582061768,0.324617803096771,0.271251231431961,0.92349636554718,0.271251767873764,0.135753512382507,0.981397986412048,0.135753720998764,0.0771115943789482,0.978334546089172,0.192134112119675,0.155218228697777,0.912262976169586,0.3790562748909,
0.215943649411201,0.920864582061768,0.324617803096771,0.107755593955517,0.980688393115997,0.163214772939682,0.0115262707695365,0.964617311954498,0.263402193784714,0.0218902062624693,0.869662165641785,0.493161767721176,0.0843572989106178,0.895177006721497,0.437655329704285,0.0417474992573261,0.972760379314423,0.228022918105125,0.0417474992573261,0.972760379314423,0.228022918105125,0.0843572989106178,0.895177006721497,0.437655329704285,0.155218228697777,0.912262976169586,0.3790562748909,0.0771115943789482,0.978334546089172,0.192134112119675,0.703433334827423,0.710178911685944,0.028768515214324,0.493157476186752,0.86966460943222,0.0218917690217495,0.437655448913574,0.895176827907562,0.0843575596809387,0.638455867767334,0.759221792221069,0.126318693161011,0.638455867767334,0.759221792221069,0.126318693161011,0.437655448913574,0.895176827907562,0.0843575596809387,0.379056185483932,0.912262976169586,0.155218631029129,0.565633177757263,0.790824770927429,0.233784794807434,0.565633177757263,0.790824770927429,0.233784794807434,0.379056185483932,0.912262976169586,0.155218631029129,0.324617087841034,0.920864701271057,0.215944111347198,0.490487515926361,0.807591021060944,0.32744288444519,0.490487515926361,0.807591021060944,0.32744288444519,0.324617087841034,0.920864701271057,0.215944111347198,0.271251231431961,0.92349636554718,0.271251767873764,0.411876559257507,0.812844395637512,0.411876022815704,0.411876559257507,0.812844395637512,0.411876022815704,0.271251231431961,0.92349636554718,0.271251767873764,0.215943649411201,0.920864582061768,0.324617803096771,0.327443152666092,0.807591140270233,0.490487068891525,0.327443152666092,0.807591140270233,0.490487068891525,0.215943649411201,0.920864582061768,0.324617803096771,0.155218228697777,0.912262976169586,0.3790562748909,0.233784645795822,0.790824830532074,0.565633296966553,0.233784645795822,0.790824830532074,0.565633296966553,0.155218228697777,0.912262976169586,0.3790562748909,0.0843572989106178,0.895177006721497,0.437655329704285,0.126318827271461,0.759221851825714,0.638455867767334,
0.126318827271461,0.759221851825714,0.638455867767334,0.0843572989106178,0.895177006721497,0.437655329704285,0.0218902062624693,0.869662165641785,0.493161767721176,0.028767392039299,0.710177183151245,0.703435003757477,0.000515904044732451,0.999928116798401,0.0119819957762957,0.0022163656540215,0.960098564624786,0.279653251171112,0.0115262707695365,0.964617311954498,0.263402193784714,0.00134799932129681,0.999848008155823,0.0173837449401617,0.263400882482529,0.964617669582367,0.0115275662392378,0.017384011298418,0.999848067760468,0.00134668650571257,0.0283790454268456,0.999585092067719,0.00493711978197098,0.228022545576096,0.972760498523712,0.0417478829622269,-0.019389109686017,0.999623954296112,-0.0193913634866476,0.0283790454268456,0.999585092067719,0.00493711978197098,0.017384011298418,0.999848067760468,0.00134668650571257,-0.22731252014637,0.946914672851563,-0.227336302399635,-0.175960153341293,0.966834843158722,-0.185117766261101,-0.00641589239239693,0.999957025051117,-0.00669931014999747,-0.019389109686017,0.999623954296112,-0.0193913634866476,-0.555457770824432,0.618699967861176,-0.555587112903595,-0.511464178562164,0.6405228972435,-0.572830617427826,-0.381141781806946,0.829169452190399,-0.408912181854248,-0.423170030117035,0.801125824451447,-0.423231303691864,-0.381141781806946,0.829169452190399,-0.408912181854248,-0.175960153341293,0.966834843158722,-0.185117766261101,-0.22731252014637,0.946914672851563,-0.227336302399635,-0.423170030117035,0.801125824451447,-0.423231303691864,-0.423170030117035,0.801125824451447,-0.423231303691864,-0.22731252014637,0.946914672851563,-0.227336302399635,-0.185100913047791,0.966834664344788,-0.175978735089302,-0.408871620893478,0.829161584377289,-0.381202340126038,-0.786266624927521,0.300187855958939,-0.540066719055176,-0.673198580741882,0.304879009723663,-0.673685789108276,-0.636143743991852,0.436216980218887,-0.63642430305481,-0.697950959205627,0.445862978696823,-0.560420155525208,-0.572739124298096,0.640501916408539,-0.511592924594879,-0.697950959205627,0.445862978696823,-0.560420155525208,
-0.636143743991852,0.436216980218887,-0.63642430305481,-0.555457770824432,0.618699967861176,-0.555587112903595,-0.555457770824432,0.618699967861176,-0.555587112903595,-0.636143743991852,0.436216980218887,-0.63642430305481,-0.560156166553497,0.445885211229324,-0.698148608207703,-0.511464178562164,0.6405228972435,-0.572830617427826,-0.889508903026581,0.18316937983036,-0.418596595525742,-0.889508903026581,0.18316937983036,-0.418596565723419,-0.889508783817291,0.183169350028038,-0.418596565723419,-0.417921930551529,0.18299126625061,-0.889862716197968,-0.417921900749207,0.182991296052933,-0.889862656593323,-0.417921900749207,0.182991281151772,-0.889862716197968,7.85161205385521e-07,4.39545219705906e-05,-1,7.85161148542102e-07,4.39545183326118e-05,-1,7.85161091698683e-07,4.3954514694633e-05,-1,7.85161205385521e-07,4.3954514694633e-05,-1,7.85161148542102e-07,4.39545183326118e-05,-1,7.85161148542102e-07,4.3954514694633e-05,-1,7.85161148542102e-07,4.3954514694633e-05,-1,7.85161262228939e-07,4.39545183326118e-05,-1,7.85161148542102e-07,4.39545183326118e-05,-1,7.85161319072358e-07,4.39545183326118e-05,-1,7.85161262228939e-07,4.39545183326118e-05,-1,7.85161091698683e-07,4.3954514694633e-05,-1,1.94262128161427e-07,5.62206969334511e-06,-1,1.94262099739717e-07,5.62206832910306e-06,-1,-1,4.43210592493415e-05,7.94120069258497e-07,-1,5.69100120628718e-06,1.68337535910723e-07,-1,5.95017536397791e-06,1.72536005038637e-07,-1,4.43210519733839e-05,7.94120012415078e-07,-1,4.43210556113627e-05,7.94120012415078e-07,-1,4.43210592493415e-05,7.94120069258497e-07,-1,4.43210592493415e-05,7.94120012415078e-07,-1,4.43210592493415e-05,7.94120012415078e-07,-1,4.43210556113627e-05,7.94120069258497e-07,-1,4.43210519733839e-05,7.94120069258497e-07,-1,4.43210592493415e-05,7.94120069258497e-07,-1,4.43210556113627e-05,7.94120012415078e-07,-1,4.43210592493415e-05,7.94120069258497e-07,-1,4.43210556113627e-05,7.94120069258497e-07,-1,5.95017536397791e-06,1.72536005038637e-07,-1,5.69100120628718e-06,1.68337535910723e-07,-1,0,7.61469323151687e-08,-1,0,7.61469323151687e-08,
1.07597280418759e-07,0,-1,1.07597280418759e-07,0,-1,1.94262099739717e-07,5.62206832910306e-06,-1,1.94262128161427e-07,5.62206969334511e-06,-1
			} 
			NormalsW: *394 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *240 {
				a: 0.807799935340881,0.445709466934204,0.800411522388458,0.445709466934204,0.795793294906616,0.450115025043488,0.807799935340881,0.450115025043488,0.807799935340881,0.452887296676636,0.790409207344055,0.452887296676636,0.784637570381165,0.453831553459167,0.807799935340881,0.453831553459167,0.806185781955719,0.433275818824768,0.805426239967346,0.433275818824768,0.800965964794159,0.439968585968018,0.803955614566803,0.439968585968018,0.680485785007477,0.453831553459167,0.681501269340515,0.453831553459167,0.762329757213593,0.453831553459167,0.796625852584839,0.453831553459167,0.807793617248535,0.453831553459167,0.292591959238052,0.399368524551392,0.292591959238052,0.604779243469238,0.73764842748642,0.426101207733154,0.257451862096786,0.399368524551392,0.713927924633026,0.426101207733154,0.223662167787552,0.399368524551392,0.692066967487335,0.426101207733154,0.192521423101425,0.399368494749069,0.672905623912811,0.426101207733154,0.165226340293884,0.399368494749069,0.657180309295654,0.426101207733154,0.142825812101364,0.399368494749069,0.645495355129242,0.426101207733154,0.126180768013,0.399368494749069,0.638299822807312,0.426101207733154,0.115930765867233,0.399368494749069,0.635870158672333,0.426101207733154,0.112469792366028,0.399368494749069,0.795389175415039,0.452887296676636,0.807799935340881,0.452887296676636,0.770720660686493,0.452887296676636,0.782264113426208,0.453831553459167,0.758543491363525,0.453831553459167,0.747000157833099,0.452887296676636,0.736682534217834,0.453831553459167,0.725139141082764,0.452887296676636,0.717521190643311,0.453831553459167,0.705977857112885,0.452887296676636,0.701795935630798,0.453831553459167,0.690252602100372,0.452887296676636,0.690110981464386,0.453831553459167,0.678567707538605,0.452887296676636,0.682915449142456,0.453831553459167,0.67137211561203,0.452887296676636,0.668942451477051,0.452887296676636,0.807799935340881,0.445709466934204,0.775384604930878,0.445709466934204,0.768296360969543,0.439968585968018,0.807799935340881,0.439968585968018,0.763836145401001,
0.433275818824768,0.807799935340881,0.433275818824768,0.357381939888,0.604779243469238,0.357381939888,0.399368524551392,0.637389302253723,0.433275818824768,0.637389361858368,0.433275818824768,0.635870158672333,0.426101207733154,0.641849637031555,0.439968585968018,0.641849637031555,0.439968585968018,0.648937821388245,0.445709466934204,0.648937821388245,0.445709466934204,0.668942451477051,0.452887296676636,0.658174395561218,0.450115025043488,0.658174395561218,0.450115025043488,0.784621119499207,0.450115025043488,0.807799935340881,0.450115025043488,0.112469762563705,0.399368494749069,0.639818966388702,0.433275818824768,0.647014558315277,0.433275818824768,0.658699452877045,0.433275818824768,0.674424707889557,0.433275818824768,0.693586051464081,0.433275818824768,0.715447008609772,0.433275818824768,0.739167630672455,0.433275818824768,0.644279301166534,0.439968585968018,0.65147477388382,0.439968585968018,0.663159787654877,0.439968585968018,0.678885042667389,0.439968585968018,0.698046326637268,0.439968585968018,0.719907283782959,0.439968585968018,0.743627846240997,0.439968585968018,0.651367425918579,0.445709466934204,0.658562958240509,0.445709466934204,0.670247912406921,0.445709466934204,0.685973227024078,0.445709466934204,0.705134570598602,0.445709466934204,0.726995468139648,0.445709466934204,0.750716149806976,0.445709466934204,0.75995260477066,0.450115025043488,0.736232042312622,0.450115025043488,0.714371085166931,0.450115025043488,0.695209741592407,0.450115025043488,0.679484486579895,0.450115025043488,0.667799532413483,0.450115025043488,0.660604000091553,0.450115025043488,0.773873090744019,0.452887296676636,0.793877720832825,0.445709466934204,0.784641206264496,0.450115025043488,0.807799935340881,0.433275818824768,0.807799935340881,0.439968585968018,0.35738205909729,0.604779243469238,0.35738205909729,0.399368494749069,0.807799935340881,0.426101207733154,0.112469762563705,0.604779243469238,0.807799935340881,0.426101207733154,0.112469792366028,0.604779243469238,0.762317001819611,0.426101207733154,0.115930765867233,0.604779243469238,
0.126180768013,0.604779243469238,0.142825812101364,0.604779243469238,0.165226340293884,0.604779243469238,0.192521423101425,0.604779243469238,0.223662167787552,0.604779243469238,0.257451862096786,0.604779243469238
			} 
			UVIndex: *394 {
				a: 0,1,2,3,4,5,6,7,8,9,10,11,12,13,6,14,6,15,16,7,17,18,119,20,20,119,118,22,22,118,117,24,24,117,116,26,26,116,115,28,28,115,114,30,30,114,113,32,32,113,111,34,35,36,16,15,37,38,39,40,40,39,41,42,42,41,43,44,44,43,45,46,46,45,47,48,48,47,49,50,50,49,13,51,52,53,54,55,55,54,56,57,57,56,112,110,58,18,17,59,60,61,62,33,63,64,61,60,65,66,64,63,51,67,68,69,36,35,70,71,69,68,66,65,71,70,53,52,109,72,34,111,6,39,38,6,41,39,6,43,41,6,45,43,6,47,45,6,49,47,6,13,49,31,73,60,33,29,74,73,31,27,75,74,29,25,76,75,27,23,77,76,25,21,78,77,23,112,56,79,19,19,79,78,21,73,80,63,60,74,81,80,73,75,82,81,74,76,83,82,75,77,84,83,76,78,85,84,77,56,54,86,79,79,86,85,78,80,87,65,63,81,88,87,80,82,89,88,81,83,90,89,82,84,91,90,83,85,92,91,84,54,53,93,86,86,93,92,85,37,94,70,35,40,95,94,37,42,96,95,40,44,97,96,42,46,98,97,44,48,99,98,46,51,69,100,50,50,100,99,48,53,70,94,93,93,94,95,92,92,95,96,91,91,96,97,90,90,97,98,89,89,98,99,88,88,99,100,87,87,100,69,65,12,67,51,13,35,15,38,37,6,38,15,5,101,14,6,1,102,103,2,103,101,5,2,2,5,4,3,104,8,11,105,0,105,11,1,1,11,10,102,104,108,8,8,108,9,104,105,0,3,4,7,16,36,71,52,55,57,110,108,9,108,62,61,64,66,68,67,12,14,101,103,102,10,109,106,107,72,59,107,106,58
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2065197412656, "Model::Corner", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationPivot", "Vector3D", "Vector", "",0,-79.994026184082,0
			P: "ScalingPivot", "Vector3D", "Vector", "",0,-79.994026184082,0
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "currentUVSet", "KString", "", "U", "UVSet0"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2065075608064, "Material::Default_Material5", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	AnimationStack: 2064307118400, "AnimStack::object", "" {
		Properties70:  {
			P: "LocalStart", "KTime", "Time", "",**********
			P: "LocalStop", "KTime", "Time", "",230930790000
			P: "ReferenceStart", "KTime", "Time", "",**********
			P: "ReferenceStop", "KTime", "Time", "",230930790000
		}
	}
	AnimationLayer: 2065074220240, "AnimLayer::BaseLayer", "" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Corner, Model::RootNode
	C: "OO",2065197412656,0
	
	;AnimLayer::BaseLayer, AnimStack::object
	C: "OO",2065074220240,2064307118400
	
	;Geometry::, Model::Corner
	C: "OO",2065072502000,2065197412656
	
	;Material::Default_Material5, Model::Corner
	C: "OO",2065075608064,2065197412656
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: "object"
	Take: "object" {
		FileName: "object.tak"
		LocalTime: **********,230930790000
		ReferenceTime: **********,230930790000
	}
}
